package UI.setting
{
   import UI.UIOrder;
   import UI.UIShow;
   import UI.bag.ItemsGripTipCtrl;
   import UI.base.btnList.BtnBox;
   import UI.base.button.NormalBtn;
   import UI.count.CountCtrl;
   import UI.pay.PayCtrl;
   import UI.pet.PetUI;
   import com.adobe.serialization.json.JSON2;
   import com.sounto.cf.ObjectToXml;
   import com.sounto.oldUtils.ComMethod;
   import com.sounto.utils.ClassProperty;
   import dataAll._app.active.ActiveData;
   import dataAll._app.head.HeadData;
   import dataAll._app.head.define.HeadDefine;
   import dataAll._app.head.define.HeadDefineGroup;
   import dataAll._app.edit.card.BossCardData;
   import dataAll._app.edit.card.BossCardSave;
   import dataAll._app.login.SaveData4399;
   import dataAll._app.love.LoveData;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsConst;
   import dataAll._app.space.craft.CraftData;
   import dataAll._app.union.define.MilitaryDefine;
   import dataAll._app.union.task.UnionTaskData;
   import dataAll._app.worldMap.define.MapMode;
   import dataAll._app.worldMap.save.WorldMapSaveGroup;
   import dataAll._data.ConstantDefine;
   import dataAll._player.more.MorePlayerData;
   import dataAll._player.supple.PlayerDataSupple;
   import dataAll.arms.define.ArmsDefine;
   import dataAll.arms.define.ArmsRangeDefine;
   import dataAll.arms.save.ArmsSave;
   import dataAll.book.define.IO_BookDefine;
   import dataAll.body.define.HeroDefine;
   import dataAll.equip.define.EquipColor;
   import dataAll.equip.define.EquipDefine;
   import dataAll.equip.define.EquipFatherDefine;
   import gameAll.arms.GameArmsCtrl;
   import dataAll.equip.device.DeviceDataCreator;
   import dataAll.equip.device.DeviceDefine;
   import dataAll.equip.device.DeviceSave;
   import dataAll.equip.save.EquipSave;
   import dataAll.equip.shield.ShieldDataCreator;
   import dataAll.equip.shield.ShieldDefine;
   import dataAll.equip.shield.ShieldSave;
   import dataAll.equip.weapon.WeaponDataCreator;
   import dataAll.equip.weapon.WeaponDefine;
   import dataAll.equip.weapon.WeaponSave;
   import dataAll.pet.PetData;
   import dataAll.pet.gene.define.GeneDefine;
   import dataAll.pet.gene.save.GeneSave;
   import dataAll.things.ThingsData;
   import dataAll.things.define.ThingsDefine;
   import dataAll.skill.HeroSkillData;
   import dataAll.things.creator.ThingsSemltCreator;
   import dataAll.things.define.ThingsDefine;
   import fl.motion.ColorMatrix;
   import flash.display.Sprite;
   import flash.events.MouseEvent;
   import flash.events.TimerEvent;
   import flash.external.ExternalInterface;
   import flash.filters.ColorMatrixFilter;
   import flash.net.URLRequest;
   import flash.net.navigateToURL;
   import flash.system.System;
   import flash.text.TextField;
   import flash.utils.Timer;
   import gameAll.arms.GameArmsCtrl;
   import gameAll.body.IO_NormalBody;
   import gameAll.hero.HeroBody;
   import gameAll.level.PlayMode;
   import gameAll.level.data.OverLevelShow;
   import w_test.drop.LevelDropCount;
   import dataAll.gift.define.GiftAddDefine;
   import dataAll.gift.define.GiftAddDefineGroup;
   
   public class SettingGamingBox extends BtnBox
   {
      
      public var Title:String;
      
      public var NumericalValue:int;
      
      public var Name:String;
      
      public var Aa:String;
      
      public var Bb:int;
      
      public var Bc:String;
      
      public var Cc:int;
      
      public var Dd:String;
      
      public var Ee:int;
      
      public var ArrColor0:Array;
      
      public var ArrColor1:Array;
      
      public var labelTag:Sprite = null;
      
      public var verTxt:TextField;
      
      private var saveTimer:Timer;
      
      private var SAVE_T:int = 3;
      
      private var save_t:int = -1;
      
      protected var greenFilter:ColorMatrixFilter;
      
      private var longBtnArr:Array;
      
      private var M_ROLE:String = "";
      

      
      public function SettingGamingBox()
      {
         ExternalInterface.addCallback("HeroCheating",HeroCheating);
         ExternalInterface.addCallback("BagCheating",BagCheating);
         ExternalInterface.addCallback("MapCheating",MapCheating);
         ExternalInterface.addCallback("AchieveCheating",AchieveCheating);
         ExternalInterface.addCallback("PetCheating",PetCheating);
         ExternalInterface.addCallback("TaskCheating",TaskCheating);
         ExternalInterface.addCallback("MoneyCheating",MoneyCheating);
         ExternalInterface.addCallback("FoodCheating",FoodCheating);
         ExternalInterface.addCallback("FoodCheating_rawObj",FoodCheating_rawObj);
         ExternalInterface.addCallback("FoodCheating_bookObjeating",FoodCheating_bookObj);
         ExternalInterface.addCallback("QX",QX);
         ExternalInterface.addCallback("SpaceCheating",SpaceCheating);
         ExternalInterface.addCallback("ArenaCheating",ArenaCheating);
         ExternalInterface.addCallback("UnionCheating",UnionCheating);
         ExternalInterface.addCallback("TimeCheating",TimeCheating);
         ExternalInterface.addCallback("SaveCheating",SaveCheating);
         ExternalInterface.addCallback("BuffCheating",BuffCheating);
         ExternalInterface.addCallback("BossCardCheating",BossCardCheating);
         ExternalInterface.addCallback("LittleCheating",LittleCheating);
         ExternalInterface.addCallback("TowerCheating",TowerCheating);
         ExternalInterface.addCallback("AddShieldCheating",AddShieldCheating);
         ExternalInterface.addCallback("AddPartsCheating",AddPartsCheating);
         ExternalInterface.addCallback("AddDeviceCheating",AddDeviceCheating);
         ExternalInterface.addCallback("AddWeaponCheatingheating",AddWeaponCheating);
         ExternalInterface.addCallback("AddGeneCheating",AddGeneCheating);
         ExternalInterface.addCallback("AddThingsCheating",AddThingsCheating);
         ExternalInterface.addCallback("AddEquipCheating",AddEquipCheating);
         ExternalInterface.addCallback("AddArmsCheating",AddArmsCheating);
         this.ArrColor0 = ["white","green","blue","purple","orange","red","black","darkgold","purgold","yagold"];
         this.ArrColor1 = ["白","绿","蓝","紫","橙","红","黑","暗金","紫金","氩金"];
         this.saveTimer = new Timer(1000);
         this.longBtnArr = [];
         super();
         this.saveTimer.addEventListener(TimerEvent.TIMER,this.saveTimerFun);
      }
      
      override public function setImg(img0:Sprite) : void
      {
         var btn0:NormalBtn = null;
         elementNameArr = ["labelTag","verTxt"];
         super.setImg(img0);
         getBtn("resume").setName("切换存档");
         getBtn("restart").setName("图内修改");
         getBtn("main").setName("退出关卡");
         getBtn("save").setName("保存存档");
         getBtn("about").setName("调试后台");
         getBtn("pass").setName("获取存档");
         this.verTxt.text = ConstantDefine.getAllVer();
         var greenFilterColor0:ColorMatrix = new ColorMatrix();
         greenFilterColor0.SetHueMatrix(184);
         this.greenFilter = new ColorMatrixFilter(greenFilterColor0.GetFlatArray());
         for each(btn0 in btnArr)
         {
            if(btn0.width > 100)
            {
               this.longBtnArr.push(btn0);
            }
         }
         countBtnData(this.longBtnArr);
         this.dropBtn.setName("掉落\n查看");
         ItemsGripTipCtrl.addNormalBtnTip(this.dropBtn);
      }
      
      override protected function SET(str0:String, value0:Object) : void
      {
         if(!this[str0])
         {
            this[str0] = value0;
         }
      }
      
      private function get dropBtn() : NormalBtn
      {
         return getBtn("drop");
      }
      
      private function fleshData() : void
      {
         this.saveTimerFun();
         var gamingB0:Boolean = Gaming.LG.state != "no";
         if(gamingB0)
         {
            showBtnArr(["save","restart","main"],this.longBtnArr);
            getBtn("restart").actived = Gaming.LG.canRestartB();
            if(LevelDropCount.testB)
            {
               getBtn("restart").actived = true;
            }
         }
         else
         {
            showBtnArr(["save","about","pass","resume"],this.longBtnArr);
         }
         this.setBtnNameByMapModel();
         this.fleshDropBtn(gamingB0);
      }
      
      private function fleshDropBtn(gamingB0:Boolean) : void
      {
         this.dropBtn.visible = false;
         this.dropBtn.tipString = "";
      }
      
      private function setBtnNameByMapModel() : void
      {
         var levelName0:String = this.getLevelName();
         getBtn("restart").setName("图内修改");
         getBtn("main").setName("退出" + levelName0);
      }
      
      private function getLevelName() : String
      {
         var model0:String = Gaming.LG.mapMode;
         if(model0 == MapMode.ENDLESS)
         {
            return "无尽模式";
         }
         return "关卡";
      }
      
      private function barChange(v0:Number, label0:String) : void
      {
         if(Boolean(Gaming.PG.save))
         {
            Gaming.PG.save.setting.setValue(label0,v0);
         }
      }
      
      override public function show() : void
      {
         super.show();
         this.fleshData();
      }
      
      override public function hide() : void
      {
         super.hide();
      }
      
      override protected function btnClick(e:MouseEvent) : void
      {
         var mainStr0:String = null;
         var btn0:NormalBtn = e.target as NormalBtn;
         var levelName0:String = this.getLevelName();
         var model0:String = Gaming.LG.mapMode;
         if(btn0.label == "resume")
         {
            Gaming.uiGroup.loginUI.show();
         }
         else if(btn0.label == "restart")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("通关当前[00] 重玩关卡[01] 秒杀队友[02] 秒杀全图[03]\n寄生首领[04] 寄生尸宠[05] 副手怒气[06] 首领血量[07]\n怪物AI[08] 队友AI[09] 主角AI[10]","",this.InMapCheating);
         }
         else if(btn0.label == "main")
         {
            mainStr0 = Gaming.LG.nowLevel.define.info.overWarn;
            if(mainStr0 == "")
            {
               mainStr0 = "确定要退出" + levelName0 + "？";
            }
            if(Gaming.LG.mode == PlayMode.ARENA && !Gaming.LG.nowLevel.dat.winB)
            {
               mainStr0 = "如果退出本关，那么本场竞技挑战就会被判定为失败。\n你确定要退出关卡？";
            }
            else if(model0 == MapMode.ENDLESS)
            {
            }
            Gaming.uiGroup.alertBox.showCheck(mainStr0,"yesAndNo",0,this.mainFun);
         }
         else if(btn0.label == "save")
         {
            UIOrder.save(true,false,false,null,null,true);
         }
         else if(btn0.label == "about")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("人物类[00] 格子类[01] 地图类[02] 物品类[03]\n成就类[04] 尸宠类[05] 任务类[06] 货币类[07]\n厨艺类[08] 极品号[09] 飞船类[10] 竞技场[11]\n军队类[12] 时间类[13] 存档类[14] " + ComMethod.color("下一页[15]","#fd397b") + "\n","",this.Allcheating);
         }
         else if(btn0.label == "pass")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("获取Json数据[00] 获取Xml数据[01]","",this.CDCheating);
         }
      }
      
      public function InMapCheating(str0:String) : void
      {
         var Pet_ARR:Array = Gaming.PG.da.pet.getFightAndSuppleBodyArr();
         var WE_ARR:Array = Gaming.BG.WE_ARR;
         var ENEMY_ARR:Array = Gaming.BG.ENEMY_ARR;
         var NormalBody:IO_NormalBody = null;
         var hero:HeroBody = null;
         var boss:IO_NormalBody = Gaming.BG.filter.getEnemyBoss();
         var WD:WeaponDefine = null;
         var Arr_InMap:Array = new Array();
         var b0:IO_NormalBody = null;
         Arr_InMap = str0.split("*",str0.length);
         this.Title = Arr_InMap[0];
         this.NumericalValue = int(Arr_InMap[1]);
         if(this.Title == "通关当前" || this.Title == "00")
         {
            Gaming.LG.levelWin("r_over");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("通关当前关卡","#fd397b") + "成功!");
         }
         if(this.Title == "重玩关卡" || this.Title == "01")
         {
            Gaming.LG.restartLevel();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("重玩当前关卡","#fd397b") + "成功!");
         }
         if(this.Title == "秒杀队友" || this.Title == "02")
         {
            for each(NormalBody in WE_ARR)
            {
               Gaming.TG.hurt.toDie(NormalBody);
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("秒杀我方所有队友","#fd397b") + "成功!");
            }
         }
         if(this.Title == "秒杀全图" || this.Title == "03")
         {
            for each(NormalBody in ENEMY_ARR)
            {
               if(!NormalBody.getData().isWeMainPlayerB())
               {
                  Gaming.TG.hurt.toDie(NormalBody);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("秒杀全图所有敌人","#fd397b") + "成功!");
               }
            }
         }
         if(this.Title == "寄生首领" || this.Title == "04")
         {
            if(boss)
            {
               hero = Gaming.PG.da.hero;
               if(hero)
               {
                  hero.transCtrl.parasiticBody(boss,300);
                  Gaming.uiGroup.alertBox.showSuccess("寄生首领" + ComMethod.color(boss.getDefine().cnName,"#fd397b") + "成功!");
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showSuccess("寄生失败");
            }
         }
         if(this.Title == "寄生尸宠" || this.Title == "05")
         {
            if(Pet_ARR.length > 0)
            {
               NormalBody = Pet_ARR[0];
               hero = Gaming.PG.da.hero;
               if(hero)
               {
                  hero.transCtrl.parasiticBody(NormalBody,300);
                  Gaming.uiGroup.alertBox.showSuccess("寄生宠物" + ComMethod.color(NormalBody.getDefine().cnName,"#fd397b") + "成功!");
               }
            }
            else
            {
               Gaming.uiGroup.alertBox.showSuccess("寄生失败");
            }
         }
         if(this.Title == "副手怒气" || this.Title == "06")
         {
            for each(WD in Gaming.defineGroup.weapon.obj)
            {
               WD.anger = this.NumericalValue;
               Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("所有副手所需怒气值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "首领血量" || this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("输入生命[数值]","0",this.InMapSetBossLife);
         }
         if(this.Title == "怪物AI" || this.Title == "08")
         {
            b0 = null;
            arr0 = Gaming.BG.ENEMY_ARR;
            for each(b0 in arr0)
            {
               b0.getAi().attackAI.haveAttackOrderB = !b0.getAi().attackAI.haveAttackOrderB;
            }
            Gaming.BG.enemyAttackB = !Gaming.BG.enemyAttackB;
            Gaming.uiGroup.alertBox.showSuccess((Boolean(Gaming.BG.enemyAttackB) ? "开启" : "关闭") + "怪物攻击 " + "成功!");
         }
         if(this.Title == "队友AI" || this.Title == "09")
         {
            b0 = null;
            arr0 = Gaming.BG.WE_ARR;
            for each(b0 in arr0)
            {
               b0.getAi().attackAI.haveAttackOrderB = !b0.getAi().attackAI.haveAttackOrderB;
            }
            Gaming.BG.weAttackB = !Gaming.BG.weAttackB;
            Gaming.uiGroup.alertBox.showSuccess((Boolean(Gaming.BG.weAttackB) ? "开启" : "关闭") + "队友攻击" + "成功!");
         }
         if(this.Title == "主角AI" || this.Title == "10")
         {
            if(Gaming.LG.autoTestB)
            {
               Gaming.PG.da.hero.closeAi();
               Gaming.LG.autoTestB = false;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("关闭主角AI","#fd397b") + "成功!");
            }
            else
            {
               Gaming.PG.da.hero.openAi();
               Gaming.LG.autoTestB = true;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("开启主角AI","#fd397b") + "成功!");
            }
         }
      }
      
      public function InMapSetBossLife(str0:String) : void
      {
         var TextArray:Array = new Array();
         TextArray = str0.split("*",str0.length);
         this.Bb = TextArray[0];
         var boss0:IO_NormalBody = Gaming.BG.filter.getEnemyBoss();
         if(boss0)
         {
            if(this.Bb <= 0)
            {
               Gaming.TG.hurt.toDie(boss0);
            }
            else
            {
               boss0.getData().setLifePer(this.Bb / 100);
            }
            Gaming.uiGroup.alertBox.showSuccess("设定当前首领生命值至" + ComMethod.color(this.Bb,"#fd397b") + "%成功！");
         }
         else
         {
            Gaming.uiGroup.alertBox.showError("当前关卡没有首领！");
         }
      }
      
      public function Allcheating(str0:String) : void
      {
         var Arr_AllMenu:Array = new Array();
         Arr_AllMenu = str0.split("*",str0.length);
         this.Title = Arr_AllMenu[0];
         if(this.Title == "人物类" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("人物昵称[00*昵称] 人物等级[01*数值]\n人物经验[02*数值] 添加称号[03*称号]\n巅峰等级[04*数值] 巅峰经验[05*数值]\n" + ComMethod.color("下一页[06*可空]","#fd397b") + "\n","",this.HeroCheating);
         }
         if(this.Title == "格子类" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("武器背包[00*数值] 装备背包[01*数值]\n物品背包[02*数值] 基因背包[03*数值]\n零件背包[04*数值] 技能背包[05*数值]\n尸宠背包[06*数值] 魂卡背包[07*数值]\n武器仓库[08*数值] " + ComMethod.color("下一页[09*可空]","#fd397b") + "\n","",this.BagCheating);
         }
         if(this.Title == "地图类" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("解锁所有地图[00*可空] 通关所有地图[01*可空]\n解锁所有秘境[02*可空] 设置秘境钥匙[03*数值]\n设置扫荡次数[04*数值] 解锁所有难度[05*可空]\n","",this.MapCheating);
         }
         if(this.Title == "物品类" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("武器类[00*可空] 装备类[01*可空]\n物品类[02*可空] 基因类[03*可空]\n副手类[04*可空] 装置类[05*可空]\n零件类[06*可空] 护盾类[07*可空]\n","",this.ThingCheating);
         }
         if(this.Title == "成就类" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("解锁全部成就[00*可空]","#fd397b") + "\n杀敌数值[01*数值] boss数值[02*数值]\n死亡数值[03*数值] 副手击杀[04*数值]\n载具击杀[05*数值] 武器掉落[06*数值]\n装备掉落[07*数值] " + ComMethod.color("|下一页|[08*可空]","#fd397b") + "\n","",this.AchieveCheating);
         }
         if(this.Title == "尸宠类" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("尸宠昵称[00*昵称] 尸宠等级[01*数值]\n尸宠经验[02*数值] 头部防御[03*数值]\n战斗力[04*数值] 生命力[05*数值]\n清除所有宠物[06*可空] 添加尸宠背包[07*数值]\n基因体掉落概率[08*数值]\n","",this.PetCheating);
         }
         if(this.Title == "任务类" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("解锁主线任务[00] 解锁任务系统[01]\n接取当前任务[02] 完成当前任务[03]\n设置当前任务目标次数[04*数值]\n解锁任务[05*代码] 下一个[06]\n","00",this.TaskCheating);
         }
         if(this.Title == "货币类" || this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置银币[00*数值] 设置积分[01*数值]\n设置纪念币[02*数值] 设置小南瓜[03*数值]\n设置十年币[04*数值] 设置零件券[05*数值]\n" + ComMethod.color("下一页[06*可空]","#fd397b") + "\n","",this.MoneyCheating);
         }
         if(this.Title == "厨艺类" || this.Title == "08")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("食材数值[00*数值] 总厨艺值[01*数值]\n食材编辑[02*可空] 美食编辑[03*可空]","",this.FoodCheating);
         }
         if(this.Title == "极品号" || this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("极品存档[00] 模型1[01] 模型2[02] 模型3[03] 模型4[04] \n模型5[05] 模型6[06] 无限物品开荒号[07]","00",this.QX);
         }
         if(this.Title == "飞船类" || this.Title == "10")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置飞船等级[00*数值]\n添加飞船经验[01*数值]\n","",this.SpaceCheating);
         }
         if(this.Title == "竞技场" || this.Title == "11")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置竞技场次数[00*数值]\n设置竞技场分数[01*数值]\n","",this.ArenaCheating);
         }
         if(this.Title == "军队类" || this.Title == "12")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("完成军队任务[00*可空] 设置军衔等级[01*数值]\n快速升级军队[02*等级] 设置个人贡献[03*数值]\n清除军队数据[04*可空] 取消争霸限制[05*可空]\n设置职位权限[06*可空] 设置军队名称[07*昵称]\n设置创建军队所需黄金[08*数值]\n","",this.UnionCheating);
         }
         if(this.Title == "时间类" || this.Title == "13")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("时间加速倍数[00*倍数] 设置游戏帧数[01*帧数]\n设置新的一天[02*可空] 设置新的一周[03*可空]\n本地时间开关[04*可空] 清除双倍时间[05*可空]\n","",this.TimeCheating);
         }
         if(this.Title == "存档类" || this.Title == "14")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("查询异常原因[00] 初始化本存档[01]\n复制存档数据[02] 修复存档数据[03]\n解除异常存档[04] 设置存档异常[05]\n","",this.SaveCheating);
         }
         if(this.Title == "下一页" || this.Title == "15")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("增益类[16] 虚天塔[17] 魂卡类[18] 主角类[19]\n" + ComMethod.color("加入月影辅助交流群[20]","#AE22CE") + "\n","",this.Allcheating_Two);
         }
      }
      
      public function QX(str0:String) : void
      {
         if(str0 == "极品存档" || str0 == "00")
         {
            Gaming.api.top.getUserData("205534935","0",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型1" || str0 == "01")
         {
            Gaming.api.top.getUserData("205534935","1",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型2" || str0 == "02")
         {
            Gaming.api.top.getUserData("205534935","2",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型3" || str0 == "03")
         {
            Gaming.api.top.getUserData("205534935","3",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型4" || str0 == "04")
         {
            Gaming.api.top.getUserData("205534935","4",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型5" || str0 == "05")
         {
            Gaming.api.top.getUserData("205534935","5",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "模型6" || str0 == "06")
         {
            Gaming.api.top.getUserData("205534935","6",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
         if(str0 == "开荒号" || str0 == "07")
         {
            Gaming.api.top.getUserData("205534935","7",this.yes_getSaveByUid,this.no_getSaveByUid);
         }
      }
      
      private function yes_getSaveByUid(ud0:SaveData4399) : void
      {
         var str0:String = JSON2.encode(ud0.data);
         var obj0:Object = JSON2.decode(str0);
         Gaming.uiGroup.loginUI.outLoginEvent();
         Gaming.PG.loginData.newSave(0);
         Gaming.uiGroup.loginUI.yes_read(obj0);
      }
      
      private function no_getSaveByUid(str0:String = "") : void
      {
         Gaming.uiGroup.alertBox.showNormal(str0,"yes",null,null,"no");
      }
      
      public function Allcheating_Two(str0:String) : void
      {
         var Arr_AllMenu_Two:Array = new Array();
         Arr_AllMenu_Two = str0.split("*",str0.length);
         this.Title = Arr_AllMenu_Two[0];
         if(this.Title == "增益类" || this.Title == "16")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("双倍材料时间[00*数值] 双倍经验时间[01*数值]\n双倍武器时间[02*数值] 双倍装备时间[03*数值]\n","",this.BuffCheating);
         }
         if(this.Title == "虚天塔" || this.Title == "17")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("天塔层数[00*数值]\n幻塔层数[01*数值]","",this.TowerCheating);
         }
         if(this.Title == "魂卡类" || this.Title == "18")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("普通已抽次数[00*数值] 高级已抽次数[01*数值]\n普通抽卡次数[02*数值] 添加魂卡背包[03*数值]\n" + ComMethod.color("无限高级抽卡[04*可空]","#fd397b") + "\n" + ComMethod.color("添加自定义魂卡[05*可空]","#9933CC") + "\n","",this.BossCardCheating);
         }
         if(this.Title == "主角类" || this.Title == "19")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("表哥切换[00] 主角切换[01] 主角定义[02*昵称]\n清除溢出物品[03*可空] 解锁技能系统[04*可空]\n","",this.LittleCheating);
         }
         if(this.Title == "加入月影辅助交流群" || this.Title == "20")
         {
            navigateToURL(new URLRequest("https://qm.qq.com/q/yhMjS4qDcc"),"blank");
         }
      }
      
      public function LittleCheating(str0:String) : void
      {
         var Arr_Test:Array = new Array();
         Arr_Test = str0.split("*",str0.length);
         this.Title = Arr_Test[0];
         this.Name = Arr_Test[1];
         this.NumericalValue = int(Arr_Test[1]);
         var s0:WorldMapSaveGroup = Gaming.PG.da.worldMap.saveGroup;
         var d0:HeroDefine = Gaming.defineGroup.body.getCnDefine(this.Name) as HeroDefine;
         if(this.Title == "表哥切换" || this.Title == "00")
         {
            this.yes_swapToP1();
         }
         if(this.Title == "主角切换" || this.Title == "01")
         {
            this.yes_backToP1();
         }
         if(this.Title == "主角定义" || this.Title == "02")
         {
            if(Boolean(d0))
            {
               Gaming.PG.da.heroData.def = d0;
               Gaming.uiGroup.alertBox.showSuccess("设置成功!!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("设置失败,没有找到英雄单位!!");
            }
         }
         if(this.Title == "清除溢出物品" || this.Title == "03")
         {
            Gaming.PG.DATA.arms.delNoPositionItems();
            Gaming.uiGroup.alertBox.showSuccess("清除溢出物品成功");
         }
         if(this.Title == "解锁技能系统" || this.Title == "04")
         {
            s0.unlockOne("XiChi");
            s0.winOne("XiChi",0);
            Gaming.uiGroup.mainUI.fleshBtn();
            Gaming.uiGroup.alertBox.showSuccess("解锁技能系统成功");
         }
      }
      
      public function BossCardCheating(str0:String) : void
      {
         var Arr_BossCard:Array = new Array();
         Arr_BossCard = str0.split("*",str0.length);
         this.Title = Arr_BossCard[0];
         this.NumericalValue = int(Arr_BossCard[1]);
         if(this.Title == "普通已抽次数" || this.Title == "00")
         {
            Gaming.PG.save.bossCard.num = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("普通已抽次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "高级已抽次数" || this.Title == "01")
         {
            Gaming.PG.save.bossCard.hNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("高级已抽次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "普通抽卡次数" || this.Title == "02")
         {
            Gaming.PG.save.bossCard.pkWinStar = Number(this.NumericalValue - 40);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("普通抽卡次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "添加魂卡背包" || this.Title == "03")
         {
            Gaming.PG.save.bossCard.bag = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("添加魂卡背包格子","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "无限高级抽卡" || this.Title == "04")
         {
            Gaming.PG.save.bossCard.num = Number(200);
            Gaming.PG.save.bossCard.hNum = Number(-999999);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("无限高级抽卡","#fd397b") + "成功!");
         }
         if(this.Title == "添加自定义魂卡" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("请输入Xml格式的代码","",this.tianjiahunka);
         }
      }
      
      public function tianjiahunka(str0:String) : void
      {
         var xml0:XML = null;
         var s0:BossCardSave = null;
         var da0:BossCardData = null;
         if(str0 != "")
         {
            try
            {
               xml0 = new XML(str0);
               s0 = new BossCardSave();
               s0.inData_byXML(xml0);
               da0 = Gaming.PG.da.bossCard.addSave(s0) as BossCardData;
               Gaming.uiGroup.alertBox.showSuccess("成功添加魂卡" + da0.cnName);
            }
            catch(e:Error)
            {
               Gaming.uiGroup.alertBox.showSuccess("Xml格式不正确");
            }
         }
         else
         {
            Gaming.uiGroup.alertBox.showSuccess("空");
         }
      }
      
      public function TowerCheating(str0:String) : void
      {
         var Arr_Tower:Array = new Array();
         Arr_Tower = str0.split("*",str0.length);
         this.Title = Arr_Tower[0];
         this.NumericalValue = int(Arr_Tower[1]);
         if(this.Title == "天塔" || this.Title == "00")
         {
            Gaming.PG.SAVE.tower.unendLv = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("天塔层数设置为" + this.NumericalValue + "层成功!");
         }
         if(this.Title == "虚塔" || this.Title == "01")
         {
            Gaming.PG.SAVE.tower.blv = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("虚塔层数设置为" + this.NumericalValue + "层成功!");
         }
      }
      
      public function BuffCheating(str0:String) : void
      {
         var Arr_Buff:Array = new Array();
         Arr_Buff = str0.split("*",str0.length);
         this.Title = Arr_Buff[0];
         this.NumericalValue = int(Arr_Buff[1]);
         if(this.Title == "双倍材料时间" || this.Title == "00")
         {
            Gaming.PG.SAVE.time.doubleMaterialsDropTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("双倍材料时间设置为" + this.NumericalValue + "成功!");
         }
         if(this.Title == "双倍经验时间" || this.Title == "01")
         {
            Gaming.PG.SAVE.time.doubleExpTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("双倍经验时间设置为" + this.NumericalValue + "成功!");
         }
         if(this.Title == "双倍武器时间" || this.Title == "02")
         {
            Gaming.PG.SAVE.time.doubleArmsDropTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("双倍武器时间设置为" + this.NumericalValue + "成功!");
         }
         if(this.Title == "双倍装备时间" || this.Title == "03")
         {
            Gaming.PG.SAVE.time.doubleEquipDropTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("双倍装备时间设置为" + this.NumericalValue + "成功!");
         }
      }
      
      public function SaveCheating(str0:String) : void
      {
         if(str0 == "查询异常原因" || str0 == "00")
         {
            Gaming.uiGroup.alertBox.showSuccess("异常原因:\n" + ComMethod.color(Gaming.PG.save.main.zuobiReason,"#fd397b"));
         }
         if(str0 == "初始化本存档" || str0 == "01")
         {
            Gaming.PG.initSave();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("初始化本存档","#fd397b") + "成功!");
         }
         if(str0 == "复制存档数据" || str0 == "02")
         {
            System.setClipboard(JSON2.encode(ClassProperty.copyObj(Gaming.PG.save)));
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("已复制存档数据到剪辑板","#fd397b"));
         }
         if(str0 == "修复存档数据" || str0 == "03")
         {
            PlayerDataSupple.dealSummer(Gaming.PG.da);
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("修复存档数据","#fd397b") + "成功!");
         }
         if(str0 == "解除异常存档" || str0 == "04")
         {
            Gaming.PG.save.main.isZuobiB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解除异常存档","#fd397b") + "成功!");
         }
         if(str0 == "设置存档异常" || str0 == "05")
         {
            Gaming.PG.save.main.isZuobiB = true;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置存档异常","#fd397b") + "成功!");
         }
      }
      
      public function TimeCheating(str0:String) : void
      {
         var Arr_Time:Array = new Array();
         Arr_Time = str0.split("*",str0.length);
         this.Title = Arr_Time[0];
         this.NumericalValue = int(Arr_Time[1]);
         if(this.Title == "时间加速倍数" || this.Title == "00")
         {
            CountCtrl.onlineTimePer = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("在线时间加速倍数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "倍成功!");
         }
         if(this.Title == "设置游戏帧数" || this.Title == "01")
         {
            Gaming.ME.stage.frameRate = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("游戏帧数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#FFFF00") + "成功!");
         }
         if(this.Title == "设置新的一天" || this.Title == "02")
         {
            Gaming.PG.da.newDayCtrl(Gaming.api.save.getNowServerDate().getStr());
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置新的一天","#fd397b") + "执行成功!");
         }
         if(this.Title == "设置新的一周" || this.Title == "03")
         {
            Gaming.PG.da.newWeek(Gaming.api.save.getNowServerDate().getStr());
            UIShow.main();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置新的一周","#fd397b") + "执行成功!");
         }
         if(this.Title == "本地时间开关" || this.Title == "04")
         {
            Gaming.api.save.localTimeB = !Gaming.api.save.localTimeB;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("本地时间开关:" + Gaming.api.save.localTimeB,"#fd397b"));
         }
         if(this.Title == "清除双倍时间" || this.Title == "05")
         {
            Gaming.PG.da.time.clearAllTime();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清除所有双倍时间","#fd397b") + "成功!");
         }
      }
      
      public function UnionCheating(str0:String) : void
      {
         var Md:MilitaryDefine = null;
         var Arr_Union:Array = new Array();
         Arr_Union = str0.split("*",str0.length);
         this.Title = Arr_Union[0];
         this.Name = Arr_Union[1];
         this.NumericalValue = int(Arr_Union[1]);
         if(this.Title == "完成军队任务" || this.Title == "00")
         {
            UnionTaskData.TEST_COMPLETE_B = true;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("完成所有军队任务","#fd397b") + "成功!");
         }
         if(this.Title == "设置军衔等级" || this.Title == "01")
         {
            Md = Gaming.defineGroup.union.military.getDefine(this.NumericalValue + "");
            if(Boolean(Md))
            {
               Gaming.testCtrl.cheating.tempContribution = Md.totalMust;
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("设置军衔等级为" + this.NumericalValue + "，" + Md.cnName + "，" + Md.totalMust,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "快速升级军队" || this.Title == "02")
         {
            Gaming.api.union.grow.doTask(Gaming.getSaveIndex(),this.NumericalValue + "");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("快速升级军队","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
         }
         if(this.Title == "设置个人贡献" || this.Title == "03")
         {
            Gaming.testCtrl.cheating.tempContribution = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("个人贡献","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "清除军队数据" || this.Title == "04")
         {
            Gaming.PG.da.union.clearInfo();
            Gaming.testCtrl.cheating.haveUnionDataB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清除军队数据","#fd397b") + "成功!");
         }
         if(this.Title == "取消争霸限制" || this.Title == "05")
         {
            UnionUI.battleLimitB = false;
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("取消争霸限制","#fd397b") + "成功!");
         }
         if(this.Title == "设置职位权限" || this.Title == "06")
         {
            UnionData.ROLE_B = !UnionData.ROLE_B;
            Gaming.uiGroup.alertBox.showSuccess("设置职位权限:" + ComMethod.color(UnionData.ROLE_B,"#fd397b") + "成功!");
         }
         if(this.Title == "设置军队名称" || this.Title == "07")
         {
            if(Boolean(this.unionData.nowUnion))
            {
               this.unionData.nowUnion.title = this.Name;
               Gaming.uiGroup.alertBox.showSuccess("设置名称：" + ComMethod.color(this.Name,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "设置创建军队所需黄金" || this.Title == "08")
         {
            Gaming.uiGroup.unionUI.topBoard.addUnionMustMoney = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("创建军队所需黄金","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      public function ArenaCheating(str0:String) : void
      {
         var Arr_Arena:Array = new Array();
         Arr_Arena = str0.split("*",str0.length);
         this.Title = Arr_Arena[0];
         this.NumericalValue = int(Arr_Arena[1]);
         if(this.Title == "设置竞技场次数" || this.Title == "00")
         {
            Gaming.PG.save.arena.todayNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置竞技场次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "次成功!");
         }
         if(this.Title == "设置竞技场分数" || this.Title == "01")
         {
            Gaming.PG.save.arena.score = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置竞技场分数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "分成功!");
         }
      }
      
      public function SpaceCheating(str0:String) : void
      {
         var Cd:CraftData = null;
         var Arr_Space:Array = new Array();
         Arr_Space = str0.split("*",str0.length);
         this.Title = Arr_Space[0];
         this.NumericalValue = int(Arr_Space[1]);
         if(this.Title == "设置飞船等级" || this.Title == "00")
         {
            Cd = Gaming.PG.da.space.craft.getNowData();
            if(Cd)
            {
               Cd.setLevel(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置飞船等级" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
            }
         }
         if(this.Title == "添加飞船经验" || this.Title == "01")
         {
            Gaming.PG.da.space.addNowCraftExp(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "飞船经验值成功!");
         }
      }
      
      public function FoodCheating(str0:String) : void
      {
         var Arr_Food:Array = new Array();
         Arr_Food = str0.split("*",str0.length);
         this.Title = Arr_Food[0];
         this.NumericalValue = int(Arr_Food[1]);
         if(this.Title == "食材数量" || this.Title == "00")
         {
            Gaming.PG.da.food.addRawAll(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("所有食材数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "总厨艺值" || this.Title == "01")
         {
            Gaming.PG.da.food.save.profiAll = Number(this.NumericalValue);
            Gaming.PG.da.food.addProfi(0);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("总厨艺值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "食材编辑" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("洋葱[00*数值] 青椒[01*数值] 土豆[02*数值]\n番茄[03*数值] 胡萝卜[04*数值] 鸡蛋[05*数值]\n鸡肉[06*数值] 猪肉[07*数值] 牛肉[08*数值]\n","",this.FoodCheating_rawObj);
         }
         if(this.Title == "美食编辑" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("炒土豆丝[00*数值] 胡萝卜番茄汁[01*数值]\n孜然土豆[02*数值] 洋葱拌番茄[03*数值]\n胡萝卜土豆丁[04*数值] 青椒炒蛋[05*数值]\n鸡蛋土豆泥[06*数值] 番茄炒蛋[07*数值]\n胡萝卜炒蛋[08*数值] 下一页[09*可空]\n","",this.FoodCheating_bookObj);
         }
      }
      
      public function FoodCheating_bookObj(str0:String) : void
      {
         var Arr_Money:Array = new Array();
         Arr_Money = str0.split("*",str0.length);
         this.Title = Arr_Money[0];
         this.NumericalValue = int(Arr_Money[1]);
         var obj:Object = {};
         obj = JSON2.decode(JSON2.encode(Gaming.PG.save.food.bookObj.saveObj));
         if(this.Title == "炒土豆丝" || this.Title == "00")
         {
            obj.potato_pepper = Number(this.NumericalValue);
         }
         if(this.Title == "胡萝卜番茄汁" || this.Title == "01")
         {
            obj.carrot_tomato = Number(this.NumericalValue);
         }
         if(this.Title == "孜然土豆" || this.Title == "02")
         {
            obj.onion_potato = Number(this.NumericalValue);
         }
         if(this.Title == "洋葱拌番茄" || this.Title == "03")
         {
            obj.onion_tomato = Number(this.NumericalValue);
         }
         if(this.Title == "胡萝卜土豆丁" || this.Title == "04")
         {
            obj.carrot_potato = Number(this.NumericalValue);
         }
         if(this.Title == "青椒炒蛋" || this.Title == "05")
         {
            obj.pepper_egg = Number(this.NumericalValue);
         }
         if(this.Title == "鸡蛋土豆泥" || this.Title == "06")
         {
            obj.egg_potato = Number(this.NumericalValue);
         }
         if(this.Title == "番茄炒蛋" || this.Title == "07")
         {
            obj.tomato_egg = Number(this.NumericalValue);
         }
         if(this.Title == "胡萝卜炒蛋" || this.Title == "08")
         {
            obj.carrots_egg = Number(this.NumericalValue);
         }
         if(this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("洋葱鸡蛋饼[10*数值] 青椒炒鸡胸肉[11*数值]\n番茄鸡片[12*数值] 洋葱蒸鸡[13*数值]\n农家小炒肉[14*数值] 肉末蒸蛋[15*数值]\n肉末土豆[16*数值] 番茄酱排骨[17*数值]\n猪肉洋葱馅饼[18*数值] 下一页[19*可空]\n","",this.FoodCheating_bookObj);
            return;
         }
         if(this.Title == "洋葱鸡蛋饼" || this.Title == "10")
         {
            obj.onion_egg = Number(this.NumericalValue);
         }
         if(this.Title == "青椒炒鸡胸" || this.Title == "11")
         {
            obj.pepper_chicken = Number(this.NumericalValue);
         }
         if(this.Title == "番茄鸡片" || this.Title == "12")
         {
            obj.tomato_chicken = Number(this.NumericalValue);
         }
         if(this.Title == "洋葱蒸鸡" || this.Title == "13")
         {
            obj.onion_chicken = Number(this.NumericalValue);
         }
         if(this.Title == "农家小炒肉" || this.Title == "14")
         {
            obj.pig_pepper = Number(this.NumericalValue);
         }
         if(this.Title == "肉末蒸蛋" || this.Title == "15")
         {
            obj.pig_egg = Number(this.NumericalValue);
         }
         if(this.Title == "肉末土豆" || this.Title == "16")
         {
            obj.pig_potato = Number(this.NumericalValue);
         }
         if(this.Title == "番茄酱排骨" || this.Title == "17")
         {
            obj.pig_tomato = Number(this.NumericalValue);
         }
         if(this.Title == "猪肉洋葱馅饼" || this.Title == "18")
         {
            obj.pig_onion = Number(this.NumericalValue);
         }
         if(this.Title == "19")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("青椒牛肉[20*数值] 滑蛋牛肉[21*数值]\n亲子丼[22*数值] 宫保鸡丁[23*数值]\n黄焖鸡[24*数值] 木须肉[25*数值]\n鱼香肉丝[26*数值] 土豆焖牛腩[27*数值]\n番茄土豆炖牛肉[28*数值] 牛肉汉堡[29*数值]\n","",this.FoodCheating_bookObj);
            return;
         }
         if(this.Title == "青椒牛肉" || this.Title == "20")
         {
            obj.cattle_pepper = Number(this.NumericalValue);
         }
         if(this.Title == "滑蛋牛肉" || this.Title == "21")
         {
            obj.cattle_egg = Number(this.NumericalValue);
         }
         if(this.Title == "亲子丼" || this.Title == "22")
         {
            obj.oyakodon = Number(this.NumericalValue);
         }
         if(this.Title == "宫保鸡丁" || this.Title == "23")
         {
            obj.gongBao = Number(this.NumericalValue);
         }
         if(this.Title == "黄焖鸡" || this.Title == "24")
         {
            obj.huangMen = Number(this.NumericalValue);
         }
         if(this.Title == "木须肉" || this.Title == "25")
         {
            obj.muXu = Number(this.NumericalValue);
         }
         if(this.Title == "鱼香肉丝" || this.Title == "26")
         {
            obj.yuxiang = Number(this.NumericalValue);
         }
         if(this.Title == "土豆焖牛腩" || this.Title == "27")
         {
            obj.cattle_potato_carrot = Number(this.NumericalValue);
         }
         if(this.Title == "番茄土豆炖牛肉" || this.Title == "28")
         {
            obj.cattle_potato_tomato = Number(this.NumericalValue);
         }
         if(this.Title == "牛肉汉堡" || this.Title == "29")
         {
            obj.beefBurger = Number(this.NumericalValue);
         }
         Gaming.PG.save.food.bookObj.saveObj = obj;
         Gaming.uiGroup.alertBox.showSuccess("设置美食成功!");
      }
      
      public function FoodCheating_rawObj(str0:String) : void
      {
         var Arr_Money:Array = new Array();
         Arr_Money = str0.split("*",str0.length);
         this.Title = Arr_Money[0];
         this.NumericalValue = int(Arr_Money[1]);
         var obj:Object = {};
         obj = JSON2.decode(JSON2.encode(Gaming.PG.save.food.rawObj.saveObj));
         if(this.Title == "洋葱" || this.Title == "00")
         {
            obj.onion = Number(this.NumericalValue);
         }
         if(this.Title == "青椒" || this.Title == "01")
         {
            obj.pepper = Number(this.NumericalValue);
         }
         if(this.Title == "土豆" || this.Title == "02")
         {
            obj.potato = Number(this.NumericalValue);
         }
         if(this.Title == "番茄" || this.Title == "03")
         {
            obj.tomato = Number(this.NumericalValue);
         }
         if(this.Title == "胡萝卜" || this.Title == "04")
         {
            obj.carrot = Number(this.NumericalValue);
         }
         if(this.Title == "鸡蛋" || this.Title == "05")
         {
            obj.egg = Number(this.NumericalValue);
         }
         if(this.Title == "鸡肉" || this.Title == "06")
         {
            obj.chicken = Number(this.NumericalValue);
         }
         if(this.Title == "猪肉" || this.Title == "07")
         {
            obj.pig = Number(this.NumericalValue);
         }
         if(this.Title == "牛肉" || this.Title == "08")
         {
            obj.cattle = Number(this.NumericalValue);
         }
         Gaming.PG.save.food.rawObj.saveObj = obj;
         Gaming.uiGroup.alertBox.showSuccess("设置食材成功!");
      }
      
      public function MoneyCheating(str0:String) : void
      {
         var Arr_Money:Array = new Array();
         Arr_Money = str0.split("*",str0.length);
         this.Title = Arr_Money[0];
         this.NumericalValue = int(Arr_Money[1]);
         if(this.Title == "设置银币" || this.Title == "00")
         {
            Gaming.PG.da.main.save.coin = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("银币数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置积分" || this.Title == "01")
         {
            Gaming.PG.da.main.save.score = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("积分数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置纪念币" || this.Title == "02")
         {
            Gaming.PG.da.main.save.anniCoin = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("纪念币数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置小南瓜" || this.Title == "03")
         {
            Gaming.PG.da.main.save.pumpkin24 = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("小南瓜数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置十年币" || this.Title == "04")
         {
            Gaming.PG.da.main.save.tenCoin = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("十年币数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置零件券" || this.Title == "05")
         {
            Gaming.PG.da.main.save.partsC = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("零件券数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下一页" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("设置粽子[06*数值] 黄金累充[07*数值]\n设置黄金[08*数值]\n复制购买记录[09*可空]\n","",this.MoneyCheating_Two);
         }
      }
      
      public function MoneyCheating_Two(str0:String) : void
      {
         var Arr_Hero:Array = new Array();
         var Arr_Money_Two:Array = new Array();
         Arr_Money_Two = str0.split("*",str0.length);
         Arr_Hero = str0.split("*",str0.length);
         this.Title = Arr_Money_Two[0];
         this.NumericalValue = int(Arr_Money_Two[1]);
         this.Bb = Arr_Hero[1];
         if(this.Title == "设置粽子" || this.Title == "06")
         {
            Gaming.PG.da.main.save.zongzi25 = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("粽子数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "黄金累充" || this.Title == "07")
         {
            Gaming.api.shop.test_totalRecharged = this.Bb;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("黄金累充","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置黄金" || this.Title == "08")
         {
            Gaming.api.shop.test_balance = this.Bb;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("黄金数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            PayCtrl.getBalance();
         }
         if(this.Title == "复制购买记录" || this.Title == "09")
         {
            System.setClipboard(TestCount_pay.showOnePropsCountObj(Gaming.PG.save.pay.obj));
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("复制购买记录","#fd397b") + "成功！");
         }
      }
      
      public function TaskCheating(str0:String) : void
      {
         var Arr_Task:Array = new Array();
         var dataArr:Array = Gaming.PG.save.task.dataArr;
         var state:String = "";
         Arr_Task = str0.split("*",str0.length);
         this.Title = Arr_Task[0];
         this.NumericalValue = int(Arr_Task[1]);
         this.Bb = Arr_Task[1];
         if(this.Title == "解锁主线任务" || this.Title == "00")
         {
            Gaming.PG.da.task.unlockAllByType("main");
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有主线任务","#fd397b") + "成功!");
         }
         if(this.Title == "解锁任务系统" || this.Title == "01")
         {
            Gaming.PG.da.worldMap.saveGroup.winOne("BaiLu",0);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁任务系统","#fd397b") + "成功!");
         }
         if(this.Title == "接取当前任务" || this.Title == "02")
         {
            Gaming.uiGroup.taskUI.test_nowTaskGet();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("接取当前任务","#fd397b") + "成功!");
         }
         if(this.Title == "完成当前任务" || this.Title == "03")
         {
            Gaming.uiGroup.taskUI.test_nowTaskComplete();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("完成当前任务","#fd397b") + "成功!");
         }
         if(this.Title == "设置当前任务目标次数" || this.Title == "04")
         {
            Gaming.uiGroup.taskUI.test_setTaskNum(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置当前任务目标次数:" + ComMethod.color(this.NumericalValue,"#fd397b") + "次成功!");
         }
         if(this.Title == "解锁任务" || this.Title == "05")
         {
            Gaming.PG.da.task.unlockAllByType(this.Bb);
            Gaming.uiGroup.alertBox.showSuccess("解锁:" + ComMethod.color(this.Bb,"#fd397b") + "中的所有任务成功!");
         }
         if(this.Title == "下一页" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("解锁任务[07*数字]\n注:1~12为每日~活动 13为全部\n完成所有已解锁任务[08*数字]\n注:填0为无奖励,领取填1有奖励\n","07",this.TaskCheating);
            return;
         }
         if(this.Title == "解锁任务-1" || this.Title == "07")
         {
            if(this.Bb == "1")
            {
               Gaming.PG.da.task.unlockAllByType("day");
               Gaming.uiGroup.alertBox.showSuccess("解锁每日任务完毕");
            }
            if(this.Bb == "2")
            {
               Gaming.PG.da.task.unlockAllByType("treasure");
               Gaming.uiGroup.alertBox.showSuccess("解锁寻宝任务完毕");
            }
            if(this.Bb == "3")
            {
               Gaming.PG.da.task.unlockAllByType("extra");
               Gaming.uiGroup.alertBox.showSuccess("解锁副本任务完毕");
            }
            if(this.Bb == "4")
            {
               Gaming.PG.da.task.unlockAllByType("king");
               Gaming.uiGroup.alertBox.showSuccess("解锁擒王任务完毕");
            }
            if(this.Bb == "5")
            {
               Gaming.PG.da.task.unlockAllByType("main");
               Gaming.uiGroup.alertBox.showSuccess("解锁主线任务完毕");
            }
            if(this.Bb == "6")
            {
               Gaming.PG.da.task.unlockAllByType("memory");
               Gaming.uiGroup.alertBox.showSuccess("解锁回忆任务完毕");
            }
            if(this.Bb == "7")
            {
               Gaming.PG.da.task.unlockAllByType("deputy");
               Gaming.uiGroup.alertBox.showSuccess("解锁支线任务完毕");
            }
            if(this.Bb == "8")
            {
               Gaming.PG.da.task.unlockAllByType("spread");
               Gaming.uiGroup.alertBox.showSuccess("解锁外传任务完毕");
            }
            if(this.Bb == "9")
            {
               Gaming.PG.da.task.unlockAllByType("bwall");
               Gaming.uiGroup.alertBox.showSuccess("解锁破壁任务完毕");
            }
            if(this.Bb == "10")
            {
               Gaming.PG.da.task.unlockAllByType("custom");
               Gaming.uiGroup.alertBox.showSuccess("解锁反客任务完毕");
            }
            if(this.Bb == "11")
            {
               Gaming.PG.da.task.unlockAllByType("week");
               Gaming.uiGroup.alertBox.showSuccess("解锁每周任务完毕");
            }
            if(this.Bb == "12")
            {
               Gaming.PG.da.task.unlockAllByType("active");
               Gaming.uiGroup.alertBox.showSuccess("解锁活动任务完毕");
            }
            if(this.Bb == "13")
            {
               Gaming.PG.da.task.unlockAllByType("day");
               Gaming.PG.da.task.unlockAllByType("treasure");
               Gaming.PG.da.task.unlockAllByType("extra");
               Gaming.PG.da.task.unlockAllByType("king");
               Gaming.PG.da.task.unlockAllByType("main");
               Gaming.PG.da.task.unlockAllByType("memory");
               Gaming.PG.da.task.unlockAllByType("deputy");
               Gaming.PG.da.task.unlockAllByType("spread");
               Gaming.PG.da.task.unlockAllByType("bwall");
               Gaming.PG.da.task.unlockAllByType("custom");
               Gaming.PG.da.task.unlockAllByType("week");
               Gaming.PG.da.task.unlockAllByType("active");
               Gaming.uiGroup.alertBox.showSuccess("解锁所有任务完毕");
            }
         }
         if(this.Title == "完成所有已解锁任务" || this.Title == "08")
         {
            if(this.Bb == "0")
            {
               state = "over";
            }
            if(this.Bb == "1")
            {
               state = "complete";
            }
            for each(var obj in dataArr)
            {
               obj.state = state;
            }
            Gaming.uiGroup.alertBox.showSuccess("完成所有已解锁!\n注;要是有任务没完成那就一件通关所有地图,人物等级调成99,或者解锁一下任务");
         }
      }
      
      public function PetCheating(str0:String) : void
      {
         var Pd:PetData = null;
         var Arr_Pet:Array = new Array();
         Arr_Pet = str0.split("*",str0.length);
         this.Title = Arr_Pet[0];
         this.Name = Arr_Pet[1];
         this.NumericalValue = int(Arr_Pet[1]);
         if(this.Title == "尸宠昵称" || this.Title == "00")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.playerName = this.Name;
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠昵称为" + ComMethod.color(this.Name,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "尸宠等级" || this.Title == "01")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.level = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
            }
         }
         if(this.Title == "尸宠经验" || this.Title == "02")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.exp = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠经验为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "头部防御" || this.Title == "03")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.headAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠头部防御强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "战斗力" || this.Title == "04")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.dpsAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠战斗力强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "生命力" || this.Title == "05")
         {
            Pd = PetUI.getNowData();
            if(Pd is PetData)
            {
               Pd.base.save.lifeAdd = Number(this.NumericalValue);
               Gaming.uiGroup.alertBox.showSuccess("设置当前尸宠生命力强化等级为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "清除所有宠物" || this.Title == "06")
         {
            Gaming.PG.da.pet.clearData();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("清除所有宠物","#fd397b") + "次成功！");
         }
         if(this.Title == "添加尸宠背包" || this.Title == "07")
         {
            Gaming.PG.da.petBag.addSave(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个尸宠背包成功！");
         }
         if(this.Title == "基因体掉落概率" || this.Title == "08")
         {
            var d0:GeneDefine = Gaming.uiGroup.petUI.bookBoard.nowGeneDefine;
            if(Boolean(d0))
            {
               Gaming.PG.da.pet.saveGroup.map.setDropPro(d0.name,Number(this.NumericalValue / 100),true);
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(d0.cnName,"#fd397b") + "基因体掉落概率：" + ComMethod.color(Number(this.NumericalValue / 100),"#fd397b") + "成功！");
            }
         }
      }
      
      public function AchieveCheating(str0:String) : void
      {
         var Arr_Achieve:Array = new Array();
         Arr_Achieve = str0.split("*",str0.length);
         this.Title = Arr_Achieve[0];
         this.NumericalValue = int(Arr_Achieve[1]);
         if(this.Title == "解锁全部成就" || this.Title == "00")
         {
            Gaming.PG.da.achieve.completeAll();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁全部成就","#fd397b") + "成功!");
         }
         if(this.Title == "杀敌数量" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.killNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("杀敌数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "boss数量" || this.Title == "02")
         {
            Gaming.PG.SAVE.count.killBossNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("击杀boss数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "死亡数量" || this.Title == "03")
         {
            Gaming.PG.SAVE.count.dieNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("死亡数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "副手击杀" || this.Title == "04")
         {
            Gaming.PG.save.count.weaponKillNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("副手击杀数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "载具击杀" || this.Title == "05")
         {
            Gaming.PG.SAVE.count.vehicleKillNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("载具击杀数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "武器掉落" || this.Title == "06")
         {
            Gaming.PG.SAVE.count.dropArmsNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器掉落数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "装备掉落" || this.Title == "07")
         {
            Gaming.PG.SAVE.count.dropEquipNum = int(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备掉落数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下一页" || this.Title == "08")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("不掉橙装[00*数值] 擒王等级[01*等级]\n魅惑数量[02*数值] 派生导弹[03*数值]\n馈赠数量[04*数值] 飞行高度[05*高度]\n下坠高度[06*高度] 滑翔时间[07*时间]\n黑市交易[08*数值] " + ComMethod.color("|下一页|[09*可空]","#fd397b") + "\n","",this.AchieveCheating_Two);
         }
      }
      
      public function AchieveCheating_Two(str0:String) : void
      {
         var Arr_Achieve2:Array = new Array();
         Arr_Achieve2 = str0.split("*",str0.length);
         this.Title = Arr_Achieve2[0];
         this.NumericalValue = int(Arr_Achieve2[1]);
         if(this.Title == "不掉橙装" || this.Title == "00")
         {
            Gaming.PG.SAVE.count.bossNoOrangeNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("不掉橙装数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "擒王等级" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.maxKingLevel = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("擒王等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "魅惑数量" || this.Title == "02")
         {
            Gaming.PG.SAVE.count.charmMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("魅惑数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "派生导弹" || this.Title == "03")
         {
            Gaming.PG.SAVE.count.moreMissileMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("派生导弹数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "馈赠数量" || this.Title == "04")
         {
            Gaming.PG.save.count.skillGiftNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("馈赠数量","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "飞行高度" || this.Title == "05")
         {
            Gaming.PG.SAVE.count.maxFlyHigh = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("飞行高度","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下坠高度" || this.Title == "06")
         {
            Gaming.PG.SAVE.count.maxFallHigh = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("下坠高度","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "滑翔时间" || this.Title == "07")
         {
            Gaming.PG.SAVE.count.glidingTime = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("滑翔时间","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "黑市交易" || this.Title == "08")
         {
            Gaming.PG.SAVE.count.blackMarketMaxNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("黑市交易次数","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下一页" || this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("全是银币[00*数值]\n全是橙装[01*数值]\n","",this.AchieveCheating_Three);
         }
      }
      
      public function AchieveCheating_Three(str0:String) : void
      {
         var Arr_Achieve3:Array = new Array();
         Arr_Achieve3 = str0.split("*",str0.length);
         this.Title = Arr_Achieve3[0];
         this.NumericalValue = int(Arr_Achieve3[1]);
         if(this.Title == "全是银币" || this.Title == "00")
         {
            Gaming.PG.SAVE.count.lotteryCoin = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("全是银币","#fd397b") + "次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "全是橙装" || this.Title == "01")
         {
            Gaming.PG.SAVE.count.lotteryAllOrangeNum = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("全是橙装","#fd397b") + "次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
      }
      
      public function ThingCheating(str0:String) : void
      {
         var Arr_Things:Array = new Array();
         Arr_Things = str0.split("*",str0.length);
         this.Title = Arr_Things[0];
         if(this.Title == "武器类" || this.Title == "00")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------武器类--------","#fd397b") + "\n添加指定武器[00*名字*等级]  添加所有稀有武器[01*等级]\n添加所有黑色武器[02*等级]  添加所有武器碎片[03*数值]\n\n" + ComMethod.color("注意：图鉴武器请直接在图鉴界面领取","#00ff00") + "\n","00*光锥*99",this.AddArmsCheating);
         }
         if(this.Title == "装备类" || this.Title == "01")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------装备类--------","#fd397b") + "\n添加指定装备[00*名字*颜色*等级]\n添加所有套装[01]\n添加所有时装[02]\n添加所有装备碎片[03*数值]\n","00*战神的蔑视*紫金*99",this.AddEquipCheating);
         }
         if(this.Title == "物品类" || this.Title == "02")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------物品类--------","#fd397b") + "\n添加指定物品[昵称*数值]\n添加所有物品[00*数值]\n添加指定价值物品[01*数值]\n","",this.AddThingsCheating);
         }
         if(this.Title == "基因类" || this.Title == "03")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------基因类--------","#fd397b") + "\n随机基因体[00*颜色]\n\n","",this.AddGeneCheating);
         }
         if(this.Title == "副手类" || this.Title == "04")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------副手类--------","#fd397b") + "\n添加所有副手[00*数值]\n\n","",this.AddWeaponCheating);
         }
         if(this.Title == "装置类" || this.Title == "05")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------装置类--------","#fd397b") + "\n添加所有装置[00*数值]\n\n","",this.AddDeviceCheating);
         }
         if(this.Title == "零件类" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------零件类--------","#fd397b") + "\n添加指定物品[名字*数值]  添加所有物品[00*数值]\n添加所有钥匙[01*数值]  添加普通零件[02*等级*数值]\n添加特殊零件[03*数值]  添加稀有零件[04*数值]\n" + ComMethod.color("更多选项[05]","#00ff00") + "  " + ComMethod.color("调试零件[06]","#ff0000") + "\n","00*999999",this.AddPartsCheating);
         }
         if(this.Title == "护盾类" || this.Title == "07")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput(ComMethod.color("--------护盾类--------","#fd397b") + "\n添加所有护盾[00*数值]\n\n","",this.AddShieldCheating);
         }
      }

      public function AddArmByName(name0:String, lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         var found:Boolean = false;

         // 先搜索稀有武器
         var rareArmsArr:Array = Gaming.defineGroup.bullet.rareArmsRangeArr;
         for each(d0 in rareArmsArr)
         {
            s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.def.name);
            if(s0 && (name0 == s0.cnName || name0 == d0.def.name || name0 == d0.def.cnName))
            {
               Gaming.PG.da.armsBag.addSave(s0);
               GameArmsCtrl.addArmsSaveResoure(s0);
               Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的指定武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
               found = true;
               break;
            }
         }

         // 如果在稀有武器中没找到，搜索黑色武器
         if(!found)
         {
            var blackArmsArr:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
            for each(d0 in blackArmsArr)
            {
               s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,d0.def);
               if(s0 && (name0 == s0.cnName || name0 == d0.def.name || name0 == d0.def.cnName))
               {
                  Gaming.PG.da.armsBag.addSave(s0);
                  GameArmsCtrl.addArmsSaveResoure(s0);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的指定武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
                  found = true;
                  break;
               }
            }
         }

         // 如果还没找到，尝试搜索所有武器定义（包括其他类型）
         if(!found)
         {
            var allArmsObj:Object = Gaming.defineGroup.bullet.obj["arms"];
            var armsDef:ArmsDefine = null;
            for each(armsDef in allArmsObj)
            {
               if(name0 == armsDef.cnName || name0 == armsDef.name)
               {
                  // 根据武器颜色使用不同的创建方法
                  if(armsDef.color == EquipColor.BLACK)
                  {
                     s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,armsDef);
                  }
                  else
                  {
                     s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,armsDef.name);
                  }

                  if(s0)
                  {
                     Gaming.PG.da.armsBag.addSave(s0);
                     GameArmsCtrl.addArmsSaveResoure(s0);
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(lv0,"#fd397b") + "级的指定武器" + ComMethod.color(s0.cnName,"#FFFF00") + "成功!");
                     found = true;
                     break;
                  }
               }
            }
         }

         // 如果没有找到匹配的武器，显示错误信息
         if(!found)
         {
            Gaming.uiGroup.alertBox.showError("未找到名为" + ComMethod.color(name0,"#FFFF00") + "的武器！\n请检查武器名称是否正确。");
         }
      }











      public function AddSuitByName(name0:String, color0:String, lv0:int) : void
      {
         var n:* = undefined;
         var f0:EquipFatherDefine = null;
         var proObj2:Object = null;
         var obj0:Object = Gaming.defineGroup.equip.fatherObj;
         var proObj0:Object = null;
         for(n in obj0)
         {
            f0 = obj0[n];
            if(f0.haveSuitB)
            {
               proObj2 = this.addOneSuit(f0,proObj0,name0,color0,lv0);
               if(!proObj0)
               {
                  proObj0 = proObj2;
               }
            }
         }
      }

      private function addOneSuit(f0:EquipFatherDefine, proObj0:Object, name0:String, color0:String, lv0:int) : Object
      {
         var i:int = 0;
         var n:* = undefined;
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         var newProObj0:Object = {};
         for(n in f0.partObj)
         {
            d0 = f0.partObj[n];
            if(d0 == name0)
            {
               while(i < this.ArrColor0.length)
               {
                  if(color0 == this.ArrColor1[i])
                  {
                     s0 = Gaming.defineGroup.equipCreator.getSuperSave(this.ArrColor0[i],lv0,d0.name);
                     s0.setImgName(d0.name);
                     s0.cnName = d0.cnName;
                     newProObj0[n] = s0.getTrueObj();
                     Gaming.PG.da.equipBag.addSave(s0);
                  }
                  i++;
               }
            }
         }
         return newProObj0;
      }



      // 添加特殊零件的方法
      private function addSpecialParts(num:int) : void
      {
         var d0:ThingsDefine = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var addedCount:int = 0;

         for each(d0 in arr0)
         {
            if(d0.isPartsSpecialB())
            {
               if(d0.name.indexOf("_") > 0)
               {
                  if(d0.itemsLevel == 1)
                  {
                     Gaming.PG.da.partsBag.addDataByName(d0.name, num);
                     addedCount++;
                  }
               }
            }
         }

         // 如果没找到特殊零件，添加一些默认的特殊零件
         if(addedCount == 0)
         {
            var specialPartsNames:Array = ["special_engine_1", "special_armor_1", "special_weapon_1"];
            for each(var partName:String in specialPartsNames)
            {
               Gaming.PG.da.partsBag.addDataByName(partName, num);
               addedCount++;
            }
         }
      }

      // 添加稀有零件的方法（返回添加的种类数）
      private function addRarePartsWithCount(num:int) : int
      {
         return this.addRarePartsInternal(num);
      }

      // 内部实现方法
      private function addRarePartsInternal(num:int) : int
      {
         var d0:ThingsDefine = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var addedCount:int = 0;
         var partsBag:PartsDataGroup = Gaming.PG.da.partsBag;

         // 检查零件背包空间
         var availableSpace:int = partsBag.getSpaceSiteNum();
         if(availableSpace <= 0)
         {
            trace("零件背包空间不足，无法添加稀有零件");
            return 0;
         }

         for each(d0 in arr0)
         {
            if(d0.isPartsB())
            {
               // 改进的稀有零件识别逻辑
               var isRare:Boolean = false;

               // 1. 检查是否为官方稀有零件类型
               if(d0.isPartsRareB())
               {
                  isRare = true;
               }

               // 2. 检查是否为特殊零件
               if(d0.isPartsSpecialB())
               {
                  isRare = true;
               }

               // 3. 检查是否为高等级零件（85级以上）
               if(d0.itemsLevel >= 85)
               {
                  isRare = true;
               }

               // 4. 检查名称关键词
               if(d0.cnName && (d0.cnName.indexOf("稀有") >= 0 || d0.cnName.indexOf("西游") >= 0 ||
                  d0.cnName.indexOf("传说") >= 0 || d0.cnName.indexOf("史诗") >= 0 ||
                  d0.cnName.indexOf("特殊") >= 0 || d0.cnName.indexOf("高级") >= 0))
               {
                  isRare = true;
               }

               // 5. 检查objType是否为稀有或特殊类型
               if(d0.objType == "rare" || d0.objType == "special" || d0.objType == "skill")
               {
                  isRare = true;
               }

               if(isRare)
               {
                  try
                  {
                     var addedData:ThingsData = partsBag.addDataByName(d0.name, num) as ThingsData;
                     if(addedData)
                     {
                        addedCount++;
                        trace("成功添加稀有零件: " + d0.cnName + " (类型: " + d0.objType + ", 等级: " + d0.itemsLevel + ")");
                     }
                  }
                  catch(e:Error)
                  {
                     trace("添加零件失败: " + d0.name + " - " + e.message);
                  }
               }
            }
         }

         return addedCount;
      }

      public function AddShieldCheating(str0:String) : void
      {
         var s0:ShieldSave = null;
         var d0:ShieldDefine = null;
         var Arr_AddShield:Array = new Array();
         Arr_AddShield = str0.split("*",str0.length);
         this.Title = Arr_AddShield[0];
         this.NumericalValue = int(Arr_AddShield[1]);
         if(this.Title == "添加所有护盾" || this.Title == "00")
         {
            for each(d0 in Gaming.defineGroup.shield.arr)
            {
               if(d0.name != "spongeShell" && d0.name != "loveShell" && d0.name != "crabShell")
               {
                  s0 = ShieldDataCreator.getSave(d0.name,this.NumericalValue);
                  Gaming.PG.da.equipBag.addSave(s0);
                  Gaming.uiGroup.alertBox.showSuccess("添加所有护盾" + ComMethod.color(this.NumericalValue,"#fd397b") + "个成功!");
               }
            }
         }
      }
      
      public function AddPartsCheating(str0:String) : void
      {
         var arr0:Object = Gaming.defineGroup.things.obj;
         var d0:ThingsDefine = null;
         var Arr_Addthing:Array = new Array();
         Arr_Addthing = str0.split("*",str0.length);
         this.Title = Arr_Addthing[0];
         this.NumericalValue = int(Arr_Addthing[1]);
         var foundItem:Boolean = false;

         // 先检查特殊指令
         if(this.Title == "00" || this.Title == "01" || this.Title == "02" || this.Title == "03" || this.Title == "04" || this.Title == "05" || this.Title == "06")
         {
            foundItem = true; // 标记为已找到，避免后续的错误提示
         }

         // 添加指定物品（排除特殊指令）
         if(this.Title != "添加所有物品" && this.Title != "00" && this.Title != "01" && this.Title != "02" && this.Title != "03" && this.Title != "04" && this.Title != "05" && this.Title != "06")
         {
            for each(d0 in arr0)
            {
               if(d0.cnName == this.Title)
               {
                  foundItem = true;
                  if(d0.isPartsB())
                  {
                     // 零件类物品
                     Gaming.PG.da.partsBag.addDataByName(d0.name,this.NumericalValue);
                     // 刷新零件背包UI
                     if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
                     {
                        Gaming.uiGroup.bagUI.show();
                     }
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个零件" + ComMethod.color(this.Title,"#FFFF00") + "成功!");
                  }
                  else if(d0.isShopAutoUseB())
                  {
                     // 商城自动使用物品（如月卡等）
                     Gaming.uiGroup.alertBox.showError("物品" + ComMethod.color(this.Title,"#FFFF00") + "是特殊物品，无法直接添加!");
                  }
                  else
                  {
                     // 普通物品
                     Gaming.PG.da.thingsBag.addDataByName(d0.name,this.NumericalValue);
                     // 刷新物品背包UI
                     if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
                     {
                        Gaming.uiGroup.bagUI.show();
                     }
                     Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color(this.Title,"#FFFF00") + "成功!");
                  }
                  break;
               }
            }

            if(!foundItem)
            {
               Gaming.uiGroup.alertBox.showError("找不到物品：" + ComMethod.color(this.Title,"#FFFF00") + "\n请检查物品名称是否正确！");
            }
         }

         // 添加所有物品
         if(this.Title == "添加所有物品" || this.Title == "00")
         {
            var addedCount:int = 0;
            for each(d0 in arr0)
            {
               if(!d0.isPartsB() && !d0.isShopAutoUseB() &&
                  d0.cnName != "白金月卡" && d0.cnName != "黄金月卡" &&
                  d0.cnName != "普通月卡" && d0.cnName != "稀有" &&
                  d0.cnName != "粽子测试")
               {
                  Gaming.PG.da.thingsBag.addDataByName(d0.name,this.NumericalValue);
                  addedCount++;
               }
            }
            // 刷新物品背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("全部物品","#FFFF00") + "成功!\n共添加了" + ComMethod.color(addedCount,"#fd397b") + "种物品!");
         }

         // 添加所有钥匙
         if(this.Title == "添加所有钥匙" || this.Title == "01")
         {
            Gaming.PG.da.thingsBag.addDataByName("dreamKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("courageKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("energyKey",this.NumericalValue);
            Gaming.PG.da.thingsBag.addDataByName("victoryKey",this.NumericalValue);
            // 刷新物品背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "things")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("所有钥匙","#FFFF00") + "成功!");
         }

         // 添加普通零件
         if(this.Title == "添加普通零件" || this.Title == "02")
         {
            // 检查参数数量是否正确
            if(Arr_Addthing.length < 3)
            {
               Gaming.uiGroup.alertBox.showError("参数不足！\n正确格式：02*等级*数量\n例如：02*72*10");
               return;
            }

            var lv0:int = int(Arr_Addthing[1]);
            var num0:int = int(Arr_Addthing[2]);

            // 验证参数有效性
            if(lv0 <= 0)
            {
               Gaming.uiGroup.alertBox.showError("等级必须大于0！\n当前输入等级：" + lv0);
               return;
            }

            if(num0 <= 0)
            {
               Gaming.uiGroup.alertBox.showError("数量必须大于0！\n当前输入数量：" + num0);
               return;
            }

            // 调试信息：检查普通零件名称数组
            var normalPartsArr:Array = Gaming.defineGroup.things.normalPartsNameArr;
            if(!normalPartsArr || normalPartsArr.length == 0)
            {
               Gaming.uiGroup.alertBox.showError("错误：普通零件名称数组为空！\n可能原因：\n1. 游戏数据未正确加载\n2. 普通零件定义缺失\n\n数组长度：" + (normalPartsArr ? normalPartsArr.length : "null"));
               return;
            }

            var partsStr:String = lv0 + "," + num0;
            var result1:String = Gaming.testCtrl.cheating.things.addPartsAll(partsStr,0);

            // 调试信息：显示添加的零件详情
            Gaming.uiGroup.alertBox.showSuccess(result1 + "\n调试信息：\n等级：" + lv0 + "\n数量：" + num0 + "\n普通零件种类：" + normalPartsArr.length + "种");

            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
         }

         // 添加特殊零件
         if(this.Title == "添加特殊零件" || this.Title == "03")
         {
            this.addSpecialParts(this.NumericalValue);
            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("特殊零件","#FFFF00") + "成功!");
         }

         // 添加稀有零件
         if(this.Title == "添加稀有零件" || this.Title == "04")
         {
            var rareCount:int = this.addRarePartsWithCount(this.NumericalValue);
            // 刷新零件背包UI
            if(Gaming.uiGroup.bagUI.visible && Gaming.uiGroup.bagUI.labelBox.nowLabel == "parts")
            {
               Gaming.uiGroup.bagUI.show();
            }
            if(rareCount > 0)
            {
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个稀有零件成功!\n共添加了" + ComMethod.color(rareCount,"#00ff00") + "种稀有零件!");
            }
            else
            {
               Gaming.uiGroup.alertBox.showError("未找到任何稀有零件!\n请检查游戏中是否存在稀有零件定义。");
            }
         }
      }
      
      public function AddDeviceCheating(str0:String) : void
      {
         var Arr_AddDevice:Array = new Array();
         var d0:DeviceDefine = null;
         var s0:DeviceSave = null;
         Arr_AddDevice = str0.split("*",str0.length);
         this.Title = Arr_AddDevice[0];
         this.NumericalValue = int(Arr_AddDevice[1]);
         if(this.Title == "添加所有装置" || this.Title == "00")
         {
            if(this.NumericalValue < 1)
            {
               this.NumericalValue = 1;
            }
            for each(d0 in Gaming.defineGroup.device.arr)
            {
               s0 = DeviceDataCreator.getSave(d0.name + "_1",this.NumericalValue);
               Gaming.PG.da.equipBag.addSave(s0);
               Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有装置","#fd397b") + "成功!");
            }
         }
      }
      
      public function AddWeaponCheating(str0:String) : void
      {
         var Arr_AddWeapon:Array = new Array();
         var d0:WeaponDefine = null;
         var s0:WeaponSave = null;
         Arr_AddWeapon = str0.split("*",str0.length);
         this.Title = Arr_AddWeapon[0];
         this.NumericalValue = int(Arr_AddWeapon[1]);
         if(this.Title == "添加所有副手" || this.Title == "00")
         {
            if(this.NumericalValue < 1)
            {
               this.NumericalValue = 1;
            }
            for each(d0 in Gaming.defineGroup.weapon.arr)
            {
               if(d0.lv == 1 && d0.name != "goldSpade_1" && d0.name != "miningSpade_1" && d0.name != "miningShovels_1")
               {
                  s0 = WeaponDataCreator.getSave(d0.name,this.NumericalValue);
                  Gaming.PG.da.equipBag.addSave(s0);
                  Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有副手","#fd397b") + "成功!");
               }
            }
         }
      }
      
      public function AddGeneCheating(str0:String) : void
      {
         var Arr_AddGene:Array = new Array();
         var Gs:GeneSave = null;
         var i:int = 0;
         Arr_AddGene = str0.split("*",str0.length);
         this.Title = Arr_AddGene[0];
         this.Name = Arr_AddGene[1];
         if(this.Title == "随机基因体" || this.Title == "00")
         {
            i = 0;
            while(i < this.ArrColor0.length)
            {
               if(this.Name == this.ArrColor1[i] && this.Name == this.ArrColor0[i])
               {
                  Gs = Gaming.defineGroup.geneCreator.getSave(this.ArrColor0[i],Gaming.PG.da.level,Gaming.defineGroup.gene.getRandomGene().name);
                  Gaming.PG.da.geneBag.addSave(Gs);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.ArrColor1[i],"#fd397b") + "色的随机基因体成功！");
               }
               i++;
            }
         }
      }
      
      public function AddThingsCheating(str0:String) : void
      {
         var Arr_AddThing:Array = new Array();
         Arr_AddThing = str0.split("*",str0.length);
         this.Title = Arr_AddThing[0];
         this.NumericalValue = int(Arr_AddThing[1]);
         var arr0:Object = Gaming.defineGroup.things.obj;
         var Td0:ThingsDefine = null;
         for each(Td0 in arr0)
         {
            if(Td0.cnName == this.Title)
            {
               if(!Td0.isPartsB() && !Td0.isShopAutoUseB())
               {
                  Gaming.PG.da.thingsBag.addDataByName(Td0.name,this.NumericalValue);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color(this.Title,"#fd397b") + "成功!");
               }
               if(Td0.isPartsB())
               {
                  Gaming.PG.da.partsBag.addDataByName(Td0.name,this.NumericalValue);
                  Gaming.uiGroup.alertBox.showSuccess("刷取" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color(this.Title,"#fd397b") + "成功!");
               }
            }
         }
         if(this.Title == "添加所有物品" || this.Title == "00")
         {
            if(this.NumericalValue < 1)
            {
               this.NumericalValue = 1;
            }
            var obj0:Object = Gaming.defineGroup.things.obj;
            for each(Td0 in obj0)
            {
               if(!Td0.isPartsB() && !Td0.isShopAutoUseB() && Td0.cnName != "白金月卡" && Td0.cnName != "黄金月卡" && Td0.cnName != "普通月卡" && Td0.cnName != "稀有" && Td0.cnName != "粽子测试" && Td0.cnName != "围墙碎片" && Td0.cnName != "撒旦碎片" && Td0.cnName != "未羊碎片" && Td0.cnName != "辰龙碎片" && Td0.cnName != "寅虎碎片")
               {
                  Gaming.PG.da.thingsBag.addDataByName(Td0.name,this.NumericalValue);
               }
            }
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "个" + ComMethod.color("全部物品","#fd397b") + "成功!");
         }
         if(this.Title == "添加指定价值物品" || this.Title == "01")
         {
            var s0:String = ThingsSemltCreator.getThingsStrByPrice(this.NumericalValue);
            System.setClipboard(s0);
            Gaming.uiGroup.alertBox.showSuccess("生成价值为" + ComMethod.color(this.NumericalValue,"#fd397b") + "的物品列表成功!" + "\n" + ComMethod.color("已复制到剪贴板","#fd397b"));
         }
      }
      
      public function AddEquipCheating(str0:String) : void
      {
         var d0:EquipDefine = null;
         var s0:EquipSave = null;
         var Arr_AddEquip:Array = new Array();
         Arr_AddEquip = str0.split("*",str0.length);
         this.Title = Arr_AddEquip[0];
         this.Name = Arr_AddEquip[1];
         var Color:String = String(Arr_AddEquip[2]);
         this.NumericalValue = int(Arr_AddEquip[3]);
         if(this.Title == "添加指定装备" || this.Title == "00")
         {
            this.AddSuitByName(this.Name,Color,this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的" + ComMethod.color(Color,"#fd397b") + "品质" + ComMethod.color(this.Name,"#fd397b") + "成功!");
         }
         if(this.Title == "添加所有套装" || this.Title == "01")
         {
            Gaming.testCtrl.arms.addAllSuit();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有套装","#fd397b") + "成功!");
         }
         if(this.Title == "添加所有时装" || this.Title == "02")
         {
            for each(d0 in Gaming.defineGroup.equip.fashionObj)
            {
               s0 = Gaming.defineGroup.equipCreator.getFashionSave(d0.name);
               Gaming.PG.da.equipBag.addSave(s0);
            }
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("添加所有时装","#fd397b") + "成功!");
         }
         if(this.Title == "添加所有装备碎片" || this.Title == "03")
         {
            this.AddEquipChip(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有装备碎片" + ComMethod.color(this.NumericalValue,"#fd397b") + "个成功!");
         }
      }
      
      public function AddEquipChip(v0:int) : String
      {
         var f0:EquipFatherDefine = null;
         var d0:EquipDefine = null;
         var arr0:Array = Gaming.defineGroup.equip.blackFatherArr;
         for each(f0 in arr0)
         {
            for each(d0 in f0.partObj)
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.name,v0);
            }
         }
         return undefined;
      }
      
      public function AddArmsCheating(str0:String) : void
      {
         var Arr_AddArm:Array = new Array();
         Arr_AddArm = str0.split("*",str0.length);
         this.Title = Arr_AddArm[0];
         this.NumericalValue = int(Arr_AddArm[1]);
         if(this.Title == "添加指定武器" || this.Title == "00")
         {
            this.Name = Arr_AddArm[1];
            this.NumericalValue = int(Arr_AddArm[2]);
            this.AddArmByName(this.Name,this.NumericalValue);
         }
         if(this.Title == "添加所有稀有武器" || this.Title == "01")
         {
            this.AddRareArm(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的稀有武器成功!");
         }
         if(this.Title == "添加所有黑色武器" || this.Title == "02")
         {
            this.AddBlackArm(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有" + ComMethod.color(this.NumericalValue,"#fd397b") + "级的黑色武器成功!");
         }
         if(this.Title == "添加所有武器碎片" || this.Title == "03")
         {
            this.AddArmsChip(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("添加所有武器碎片" + ComMethod.color(this.NumericalValue,"#fd397b") + "个成功!");
         }
      }
      
      public function AddRareArm(lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         lv0 = Gaming.PG.da.level;
         var arr0:Array = Gaming.defineGroup.bullet.rareArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getSuperSaveByArmsRangeName(lv0,d0.def.name);
            Gaming.PG.da.armsBag.addSave(s0);
         }
      }
      
      public function AddBlackArm(lv0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var s0:ArmsSave = null;
         lv0 = Gaming.PG.da.level;
         var arr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(d0 in arr0)
         {
            s0 = Gaming.defineGroup.armsCreator.getBlackSave(lv0,d0.def);
            Gaming.PG.da.armsBag.addSave(s0);
            GameArmsCtrl.addArmsSaveResoure(s0);
         }
      }
      
      public function AddArmsChip(v0:int) : void
      {
         var d0:ArmsRangeDefine = null;
         var arr0:Array = Gaming.defineGroup.bullet.blackArmsRangeArr;
         for each(d0 in arr0)
         {
            if(d0.def.dropLevelArr[0] < 100 && d0.def.name != "closureGun")
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.def.name,v0);
            }
         }
      }
      
      public function MapCheating(str0:String) : void
      {
         var Arr_OutMap:Array = new Array();
         Arr_OutMap = str0.split("*",str0.length);
         this.Title = Arr_OutMap[0];
         this.NumericalValue = int(Arr_OutMap[1]);
         if(this.Title == "解锁所有地图" || this.Title == "00")
         {
            Gaming.PG.da.worldMap.saveGroup.unlockAll();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有地图","#fd397b") + "成功!");
         }
         if(this.Title == "通关所有地图" || this.Title == "01")
         {
            Gaming.PG.da.worldMap.saveGroup.unlockAll();
            Gaming.PG.da.worldMap.saveGroup.winAll();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("通关所有地图","#fd397b") + "成功!");
         }
         if(this.Title == "解锁所有秘境" || this.Title == "02")
         {
            Gaming.PG.da.wilder.unlockAllWider();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有秘境","#fd397b") + "成功!");
         }
         if(this.Title == "设置秘境钥匙" || this.Title == "03")
         {
            Gaming.PG.da.wilder.saveGroup.keyNum = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置秘境钥匙为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置扫荡次数" || this.Title == "04")
         {
            Gaming.PG.da.main.save.daySweeping = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置扫荡次数为" + ComMethod.color(this.NumericalValue,"#fd397b") + "次成功！");
         }
         if(this.Title == "解锁所有难度" || this.Title == "05")
         {
            Gaming.PG.da.worldMap.saveGroup.unlockAllDiff();
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁关卡所有难度","#fd397b") + "成功!");
         }
      }

      public function AddAllHead() : void
      {
         try
         {
            var headData:HeadData = Gaming.PG.da.head;
            var headDefineGroup:HeadDefineGroup = Gaming.defineGroup.head;
            var addedCount:int = 0;
            var currentTime:String = "";

            // 获取正确的时间字符串
            try
            {
               currentTime = Gaming.api.save.getNowServerDate().getStr();
            }
            catch(timeError:Error)
            {
               // 如果获取服务器时间失败，使用简单的时间格式
               var now:Date = new Date();
               currentTime = now.getFullYear() + "-" + (now.getMonth() + 1) + "-" + now.getDate();
               trace("使用本地时间: " + currentTime);
            }

            if(headData && headData.save && headDefineGroup && currentTime != "")
            {
               // 遍历所有称号定义
               var allHeadDefines:Array = headDefineGroup.arr;
               for each(var headDefine:HeadDefine in allHeadDefines)
               {
                  if(headDefine && headDefine.name)
                  {
                     // 检查是否已经拥有该称号
                     if(!headData.save.panHaveHead(headDefine.name))
                     {
                        // 添加称号
                        headData.save.addHead(headDefine.name, currentTime);
                        addedCount++;
                        trace("添加称号: " + headDefine.cnName + " (" + headDefine.name + ")");
                     }
                  }
               }

               // 刷新称号UI
               if(Gaming.uiGroup.headUI && Gaming.uiGroup.headUI.visible)
               {
                  Gaming.uiGroup.headUI.haveBoard.fleshData();
                  Gaming.uiGroup.headUI.noBoard.fleshData();
                  Gaming.uiGroup.headUI.honorBoard.fleshData();
               }

               trace("成功添加 " + addedCount + " 个称号");
            }
            else
            {
               trace("称号系统检查失败 - headData: " + (headData != null) + ", headData.save: " + (headData && headData.save != null) + ", headDefineGroup: " + (headDefineGroup != null) + ", currentTime: " + currentTime);
            }
         }
         catch(e:Error)
         {
            trace("添加称号错误: " + e.message + "\n堆栈: " + e.getStackTrace());
         }
      }

      public function BagCheating(str0:String) : void
      {
         var Arr_Bag:Array = new Array();
         Arr_Bag = str0.split("*",str0.length);
         this.Title = Arr_Bag[0];
         this.NumericalValue = int(Arr_Bag[1]);
         if(this.Title == "武器背包" || this.Title == "00")
         {
            Gaming.PG.da.armsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "装备背包" || this.Title == "01")
         {
            Gaming.PG.da.equipBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "物品背包" || this.Title == "02")
         {
            Gaming.PG.da.thingsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("物品背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "基因背包" || this.Title == "03")
         {
            Gaming.PG.da.geneBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("基因背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "零件背包" || this.Title == "04")
         {
            Gaming.PG.da.partsBag.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("零件背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "技能背包" || this.Title == "05")
         {
            Gaming.PG.da.skill.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("技能背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "尸宠背包" || this.Title == "06")
         {
            Gaming.PG.da.pet.saveGroup.lockLen = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("尸宠背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "魂卡背包" || this.Title == "07")
         {
            Gaming.PG.save.bossCard.bag = Number(this.NumericalValue - 80);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("魂卡背包数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "武器仓库" || this.Title == "08")
         {
            Gaming.PG.da.armsHouse.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("武器仓库数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下一页" || this.Title == "09")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("装备仓库[10*数值] 清除武器[11*可空]\n清除装备[12*可空] 清除物品[13*可空]\n清除基因[14*可空] 清除零件[15*可空]\n清除技能[16*可空] 清除尸宠[17*可空]\n清除仓库[18*可空] " + ComMethod.color("解锁所有[19*可空]","#fd397b") + "\n","",this.BagCheating_Two);
         }
      }
      
      public function BagCheating_Two(str0:String) : void
      {
         var label0:String = null;
         var Arr_Bag2:Array = new Array();
         Arr_Bag2 = str0.split("*",str0.length);
         this.Title = Arr_Bag2[0];
         this.NumericalValue = int(Arr_Bag2[1]);
         if(this.Title == "装备仓库" || this.Title == "10")
         {
            Gaming.PG.equipHouse.arms.saveGroup.unlockTo(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("装备仓库数值","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "清除武器" || this.Title == "11")
         {
            Gaming.PG.da.armsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("武器背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除装备" || this.Title == "12")
         {
            Gaming.PG.da.equipBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("装备背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除物品" || this.Title == "13")
         {
            Gaming.PG.da.thingsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("物品背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除基因" || this.Title == "14")
         {
            Gaming.PG.da.geneBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("基因背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除零件" || this.Title == "15")
         {
            Gaming.PG.da.partsBag.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("零件背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除技能" || this.Title == "16")
         {
            Gaming.PG.da.skill.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("技能背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除尸宠" || this.Title == "17")
         {
            Gaming.PG.da.pet.clearData();
            Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color("尸宠背包","#fd397b") + "成功!");
         }
         if(this.Title == "清除仓库" || this.Title == "18")
         {
            label0 = Gaming.uiGroup.houseUI.labelBox.nowLabel;
            if(label0 != "")
            {
               Gaming.PG.da[label0 + "House"].clearData();
               Gaming.uiGroup.alertBox.showSuccess("清除" + ComMethod.color(label0 + "仓库","#fd397b") + "成功!");
            }
         }
         if(this.Title == "解锁所有" || this.Title == "19")
         {
            Gaming.PG.da.armsBag.saveGroup.unlockTo(80);
            Gaming.PG.da.armsHouse.saveGroup.unlockTo(324);
            Gaming.PG.da.equipBag.saveGroup.unlockTo(180);
            Gaming.PG.da.equipHouse.saveGroup.unlockTo(384);
            Gaming.PG.da.thingsBag.saveGroup.unlockTo(600);
            Gaming.PG.da.partsBag.saveGroup.unlockTo(144);
            Gaming.PG.da.more.saveGroup.unlockTo(6);
            Gaming.PG.da.moreBag.saveGroup.unlockTo(20);
            Gaming.PG.da.geneBag.saveGroup.unlockTo(360);
            Gaming.PG.da.arms.saveGroup.unlockTo(6);
            Gaming.PG.da.skill.saveGroup.unlockTo(9);
            Gaming.PG.da.skillBag.saveGroup.unlockTo(36);
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("解锁所有背包","#fd397b") + "成功!");
         }
      }
      
      public function HeroCheating(str0:String) : void
      {
         var Arr_Hero:Array = new Array();
         Arr_Hero = str0.split("*",str0.length);
         this.Title = Arr_Hero[0];
         this.Name = Arr_Hero[1];
         this.NumericalValue = int(Arr_Hero[1]);
         var head:String = "";
         if(this.Title == "人物昵称" || this.Title == "00")
         {
            Gaming.PG.DATA.base.save.playerName = this.Name;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物昵称","#fd397b") + "为" + ComMethod.color(this.Name,"#fd397b") + "成功!");
         }
         if(this.Title == "人物等级" || this.Title == "01")
         {
            Gaming.PG.DATA.base.save.level = Number(this.NumericalValue);
            Gaming.uiGroup.mainUI.show();
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
         }
         if(this.Title == "人物经验" || this.Title == "02")
         {
            Gaming.PG.DATA.base.save.exp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("人物经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "添加称号" || this.Title == "03")
         {
            if(this.Name == "全部" || this.Name == "所有")
            {
               this.AddAllHead();
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("全部人物称号","#fd397b") + "成功!");
            }
            else if(this.Name == "十年相伴")
            {
               head = "anniver10";
            }
            else if(this.Name == "武器制造大师")
            {
               head = "armsSkinCreator";
            }
            else if(this.Name == "群组达人")
            {
               head = "bbs23";
            }
            else if(this.Name == "九周年快乐")
            {
               head = "anniver9";
            }
            else if(this.Name == "成就之皇")
            {
               head = "achieveKing";
            }
            else if(this.Name == "成就之神")
            {
               head = "achieveGod";
            }
            else if(this.Name == "八周年快乐")
            {
               head = "anniver8";
            }
            else if(this.Name == "十二生肖")
            {
               head = "zodiac12";
            }
            else if(this.Name == "霞光领主")
            {
               head = "battle4";
            }
            else if(this.Name == "霞光天军")
            {
               head = "battle3";
            }
            else if(this.Name == "霞光雄狮")
            {
               head = "battle2";
            }
            else if(this.Name == "霞光劲旅")
            {
               head = "battle1";
            }
            else if(this.Name == "愚人欢乐365")
            {
               head = "joyousFool";
            }
            else if(this.Name == "爆枪七周年")
            {
               head = "anniver7";
            }
            else if(this.Name == "单身狗")
            {
               head = "singleAF";
            }
            else if(this.Name == "愚人快乐")
            {
               head = "happyFool";
            }
            else if(this.Name == "爆枪六周年")
            {
               head = "anniver6";
            }
            else if(this.Name == "六一快乐")
            {
               head = "childrenDay";
            }
            else if(this.Name == "六一起飞")
            {
               head = "childrenFly";
            }
            else if(this.Name == "爆枪五周年")
            {
               head = "anniver5";
            }
            else if(this.Name == "爆枪四周年")
            {
               head = "anniver4";
            }
            else if(this.Name == "爆枪三周年")
            {
               head = "anniver3";
            }
            else if(this.Name == "七夕眷侣")
            {
               head = "qixi";
            }
            else if(this.Name == "粽叶飘香")
            {
               head = "zongzi";
            }
            else if(this.Name == "春节快乐")
            {
               head = "happySpring";
            }
            else if(this.Name == "秘境征服者")
            {
               head = "widerAll";
            }
            else if(this.Name == "超能英雄")
            {
               head = "superHero";
            }
            else if(this.Name == "特种奇兵")
            {
               head = "specialSoldiers";
            }
            else if(this.Name == "爆头王")
            {
               head = "headshot_20";
            }
            else if(this.Name == "踏平霞光")
            {
               head = "goddiff_30";
            }
            else if(this.Name == "死神")
            {
               head = "death";
            }
            else if(this.Name == "武器收藏家")
            {
               head = "rareArms_5";
            }
            else if(this.Name == "假的大结局")
            {
               head = "fakeFinale";
            }
            else if(this.Name == "战神")
            {
               head = "dpsTop_1";
            }
            else if(this.Name == "竞技之王")
            {
               head = "arena_1";
            }
            else if(this.Name == "步枪之王")
            {
               head = "rifle_1";
            }
            else if(this.Name == "狙击之王")
            {
               head = "sniper_1";
            }
            else if(this.Name == "散弹之王")
            {
               head = "shotgun_1";
            }
            else if(this.Name == "手枪之王")
            {
               head = "pistol_1";
            }
            else if(this.Name == "火炮之王")
            {
               head = "rocket_1";
            }
            else if(this.Name == "十项全能")
            {
               head = "almighty_10";
            }
            else if(this.Name == "兵器大亨")
            {
               head = "weapon_4";
            }
            else if(this.Name == "装置大亨")
            {
               head = "device_4";
            }
            else if(this.Name == "成就大亨")
            {
               head = "achieve_70";
            }
            else if(this.Name == "成就之王")
            {
               head = "achieve_123";
            }
            else if(this.Name == "独霸一方")
            {
               head = "dominating";
            }
            else if(this.Name == "公会是我家")
            {
               head = "unionIsMyHome";
            }
            else if(this.Name == "我要升级")
            {
               head = "wantUpgrade";
            }
            else if(this.Name == "闲着蛋疼")
            {
               head = "petEvo_4";
            }
            else if(this.Name == "载具新时代")
            {
               head = "vehicleEvo_4";
            }
            else if(this.Name == "学霸")
            {
               head = "ask_5";
            }
            else if(this.Name == "全能人")
            {
               head = "heroSkill_21";
            }
            else if(this.Name == "武器锻造家")
            {
               head = "armsRemake100";
            }
            else if(this.Name == "装备锻造家")
            {
               head = "equipRemake100";
            }
            else if(this.Name == "爆枪突击")
            {
               head = "baoqiang";
            }
            else if(this.Name == "佣兵之王")
            {
               head = "gameKing";
            }
            else if(this.Name == "佣兵精英")
            {
               head = "gameSuper";
            }
            if(head != "")
            {
               Gaming.PG.da.head.save.addHead(head,Gaming.api.save.getNowServerDate().getStr());
               Gaming.uiGroup.alertBox.showSuccess("添加" + ComMethod.color("人物称号","#fd397b") + ComMethod.color(this.Name,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "巅峰等级" || this.Title == "04")
         {
            Gaming.PG.da.peak.save.lv = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("巅峰等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
         }
         if(this.Title == "巅峰经验" || this.Title == "05")
         {
            Gaming.PG.da.peak.save.exp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("巅峰经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "下一页" || this.Title == "06")
         {
            Gaming.uiGroup.alertBox.textInput.showTextInput("职务等级[07*数值] 职务经验[08*数值]\n添加队友[09*昵称] 设置功勋[10*数值]\n设置好感度[11*数值] 设置活跃度[12*数值]\n增加上场队友[13*数值] 减少上场队友[14*数值]\n","",this.MoreCheating);
         }
      }
      
      public function MoreCheating(str0:String) : void
      {
         var Arr_More:Array = new Array();
         var value0:int = 0;
         var loveData0:LoveData = null;
         var pd0:MorePlayerData = Gaming.PG.DATA as MorePlayerData;
         var da0:HeroSkillData = Gaming.uiGroup.skillUI.upgradeBox.nowData;
         Arr_More = str0.split("*",str0.length);
         this.Title = Arr_More[0];
         this.Name = Arr_More[1];
         this.NumericalValue = int(Arr_More[1]);
         var hero:String = "";
         if(this.Title == "职务等级" || this.Title == "07")
         {
            Gaming.PG.da.post.save.postLv = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("职务等级","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "级成功!");
         }
         if(this.Title == "职务经验" || this.Title == "08")
         {
            Gaming.PG.da.post.save.postExp = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("职务经验","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "添加队友" || this.Title == "09")
         {
            if(this.Name == "小樱")
            {
               hero = "Girl";
            }
            else if(this.Name == "心零")
            {
               hero = "XinLing";
            }
            else if(this.Name == "文杰表哥")
            {
               hero = "WenJie";
            }
            else if(this.Name == "藏师将军")
            {
               hero = "ZangShi";
            }
            else if(this.Name == "天鹰小美")
            {
               hero = "XiaoMei";
            }
            else if(this.Name == "摩卡")
            {
               hero = "Mocha";
            }
            else if(this.Name == "制毒师")
            {
               hero = "Doctor";
            }
            if(hero != "")
            {
               Gaming.PG.da.moreBag.addByUnitName(hero);
               Gaming.PG.da.more.swapByOther(Gaming.PG.da.moreBag,1,0);
               UIShow.main();
               Gaming.uiGroup.alertBox.showSuccess("添加队友" + ComMethod.color(this.Name,"#fd397b") + "成功!");
            }
         }
         if(this.Title == "设置功勋" || this.Title == "10")
         {
            pd0.partner.save.exploit = Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#fd397b") + "的功勋值为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置好感度" || this.Title == "11")
         {
            loveData0 = pd0.love;
            value0 = this.NumericalValue - loveData0.save.value;
            loveData0.addValue(value0);
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color(pd0.heroData.def.cnName,"#fd397b") + "的" + loveData0.getCn() + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "设置设跃度" || this.Title == "12")
         {
            ActiveData.TEST_ACTIVE = this.NumericalValue;
            Gaming.uiGroup.alertBox.showSuccess("设置" + ComMethod.color("活跃度","#fd397b") + "为" + ComMethod.color(this.NumericalValue,"#fd397b") + "成功!");
         }
         if(this.Title == "增加上场队友" || this.Title == "13")
         {
            Gaming.PG.da.more.saveGroup.lockLen += Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("目前可上场队友个数为：" + Gaming.PG.da.more.saveGroup.getCanFillLen());
         }
         if(this.Title == "减少上场队友" || this.Title == "14")
         {
            Gaming.PG.da.more.saveGroup.lockLen -= Number(this.NumericalValue);
            Gaming.uiGroup.alertBox.showSuccess("目前可上场队友个数为：" + Gaming.PG.da.more.saveGroup.getCanFillLen());
         }
      }
      
      public function CDCheating(str0:String) : void
      {
         if(str0 == "获取Json数据" || str0 == "00")
         {
            System.setClipboard(JSON2.encode(ClassProperty.copyObj(Gaming.PG.save)));
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("复制存档数据Json","#fd397b") + "成功！");
         }
         if(str0 == "获取Xml数据" || str0 == "01")
         {
            System.setClipboard(ObjectToXml.decode4399(ClassProperty.copyObj(Gaming.PG.save)));
            Gaming.uiGroup.alertBox.showSuccess(ComMethod.color("复制存档数据XML","#fd397b") + "成功！");
         }
      }
      
      private function mainFun() : void
      {
         Gaming.TG.task.lowerTaskPan(this.afterMainFun);
      }
      
      private function afterMainFun() : void
      {
         Gaming.LG.overLevel(OverLevelShow.UI_CLICK);
      }
      
      private function yes_swapToP1() : void
      {
         var bb0:Boolean = false;
         if(this.M_ROLE != "")
         {
            bb0 = Gaming.PG.da.setNewP1NameUI(this.M_ROLE);
            if(bb0)
            {
               Gaming.uiGroup.moreBox.fleshData();
               Gaming.uiGroup.alertBox.showSuccess("切换成功！");
               return;
            }
         }
         Gaming.uiGroup.alertBox.showError("切换失败！");
      }
      
      private function yes_backToP1() : void
      {
         var bb0:Boolean = Boolean(Gaming.PG.da.backP1UI());
         if(bb0)
         {
            Gaming.uiGroup.moreBox.fleshData();
            Gaming.uiGroup.alertBox.showSuccess("切换成功！");
            return;
         }
         Gaming.uiGroup.alertBox.showError("切换失败！");
      }
      
      public function saveShow() : void
      {
         this.saveTimer.start();
         this.save_t = 0;
         getBtn("save").actived = false;
         this.saveTimerFun();
      }
      
      private function saveTimerFun(e:TimerEvent = null) : void
      {
         var btn0:NormalBtn = getBtn("save");
         if(this.save_t >= this.SAVE_T)
         {
            this.save_t = -1;
            btn0.setName("保存存档");
            btn0.actived = true;
            this.saveTimer.stop();
         }
         else if(this.save_t >= 0)
         {
            ++this.save_t;
            if(this.visible)
            {
               btn0.setName("保存存档（" + (this.SAVE_T - this.save_t) + "）");
            }
         }
      }
   }
}

