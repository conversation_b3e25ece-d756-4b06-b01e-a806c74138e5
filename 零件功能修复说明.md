# 玉帝后台版零件功能修复说明

## 问题描述

在玉帝后台版中，物品类里面的零件类功能存在问题：
- 输入指令确定之后无法添加普通零件
- 提示信息显示格式为 `02*等级*数值`，但代码解析参数时存在错误

## 问题原因

原代码在 `AddPartsCheating` 函数中的问题：

```actionscript
// 原有问题代码
var lv0:int = int(Arr_Addthing[1]);
var num0:int = int(Arr_Addthing[2]);
```

当输入格式为 `02*72*10` 时：
- `Arr_Addthing[0]` = "02"
- `Arr_Addthing[1]` = "72" (等级)
- `Arr_Addthing[2]` = "10" (数量)

但是代码没有检查参数数量，也没有验证参数有效性，导致功能无法正常工作。

## 修复内容

### 1. 参数验证
添加了完整的参数验证逻辑：

```actionscript
// 检查参数数量是否正确
if(Arr_Addthing.length < 3)
{
   Gaming.uiGroup.alertBox.showError("参数不足！\n正确格式：02*等级*数量\n例如：02*72*10");
   return;
}

var lv0:int = int(Arr_Addthing[1]);
var num0:int = int(Arr_Addthing[2]);

// 验证参数有效性
if(lv0 <= 0)
{
   Gaming.uiGroup.alertBox.showError("等级必须大于0！\n当前输入等级：" + lv0);
   return;
}

if(num0 <= 0)
{
   Gaming.uiGroup.alertBox.showError("数量必须大于0！\n当前输入数量：" + num0);
   return;
}
```

### 2. 错误提示优化
- 当参数不足时，显示正确的格式说明和示例
- 当等级或数量无效时，显示具体的错误信息和当前输入值
- 提供清晰的使用指导

## 使用方法

### 正确的输入格式
在零件类功能中，添加普通零件的正确格式为：
```
02*等级*数量
```

### 使用示例
1. **添加72级零件10个**：`02*72*10`
2. **添加60级零件5个**：`02*60*5`
3. **添加90级零件1个**：`02*90*1`

### 注意事项
1. **等级限制**：零件等级会自动调整为3的倍数，最小等级为3
2. **数量限制**：数量必须大于0
3. **格式要求**：必须使用 `*` 分隔参数，不能使用其他符号

## 相关技术细节

### 零件等级处理
在 `ThingsCheating.addPartsAll` 函数中：
```actionscript
lv0 = int(lv0 / 3) * 3;  // 调整为3的倍数
if(lv0 < PartsConst.minLv)  // PartsConst.minLv = 3
{
   lv0 = PartsConst.minLv;
}
```

### 零件名称格式
普通零件的命名格式为：`基础名称_等级`
例如：`bullet_72`、`shooter_60` 等

## 测试验证

修复后的功能应该能够：
1. 正确解析输入参数
2. 验证参数有效性
3. 显示清晰的错误信息
4. 成功添加指定等级和数量的普通零件
5. 自动刷新零件背包UI

## 文件位置

修复的文件：`scripts玉帝后台版\UI\setting\SettingGamingBox.as`
修复的函数：`AddPartsCheating`
修复的代码行：1996-2030行
