package w_test.cheating
{
   import UI.test.SaveTestBox;
   import dataAll._app.parts.PartsDataGroup;
   import dataAll._app.parts.define.PartsConst;
   import dataAll.things.creator.ThingsSemltCreator;
   import dataAll.things.define.ThingsDefine;
   import dataAll.items.IO_ItemsData;
   
   public class ThingsCheating extends OneCheating
   {
      
      public function ThingsCheating()
      {
         super();
      }
      
      public function addAllThings(str0:String, v0:int) : String
      {
         var d0:ThingsDefine = null;
         if(v0 < 1)
         {
            v0 = 1;
         }
         var obj0:Object = Gaming.defineGroup.things.obj;
         for each(d0 in obj0)
         {
            if(!d0.isPartsB() && !d0.isShopAutoUseB())
            {
               Gaming.PG.da.thingsBag.addDataByName(d0.name,v0);
            }
         }
         return "添加所有物品：" + v0;
      }
      
      public function addAllKey(str0:String, v0:int) : String
      {
         if(v0 < 1)
         {
            v0 = 1;
         }
         Gaming.PG.da.thingsBag.addDataByName("dreamKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("courageKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("energyKey",v0);
         Gaming.PG.da.thingsBag.addDataByName("victoryKey",v0);
         return "添加所有钥匙：" + v0;
      }
      
      public function addPartsAll(str0:String, v0:int) : String
      {
         var n:* = undefined;
         var name0:String = null;
         var partsBag0:PartsDataGroup = null;
         var numArr0:Array = str0.split(",");
         var arr0:Array = Gaming.defineGroup.things.normalPartsNameArr;
         var lv0:int = int(numArr0[0]);
         var num0:int = int(numArr0[1]);
         var addedCount:int = 0;

         // 参数验证
         if(numArr0.length < 2)
         {
            return "参数错误：需要格式为'等级,数量'，如'72,10'";
         }

         if(num0 <= 0)
         {
            return "数量必须大于0";
         }

         // 调整等级为3的倍数（只计算一次）
         var originalLv:int = lv0;
         lv0 = int(lv0 / 3) * 3;
         if(lv0 < PartsConst.minLv)
         {
            lv0 = PartsConst.minLv;
         }

         // 检查零件数组是否存在
         if(!arr0 || arr0.length == 0)
         {
            return "错误：未找到普通零件定义数组";
         }

         partsBag0 = Gaming.PG.da.partsBag;
         for(n in arr0)
         {
            name0 = arr0[n];
            try
            {
               var result:IO_ItemsData = partsBag0.addDataByName(name0 + "_" + lv0, num0);
               if(result != null)
               {
                  addedCount++;
               }
            }
            catch(e:Error)
            {
               // 忽略单个零件添加失败，继续添加其他零件
               trace("添加零件失败：" + name0 + "_" + lv0 + "，错误：" + e.message);
            }
         }

         var resultMsg:String = "添加" + addedCount + "种" + lv0 + "级零件，每种" + num0 + "个";
         if(originalLv != lv0)
         {
            resultMsg += "（等级已调整为" + lv0 + "）";
         }
         return resultMsg;
      }
      
      public function addSpecialParts(str0:String, v0:int) : String
      {
         var d0:ThingsDefine = null;
         var partsBag0:PartsDataGroup = null;
         var arr0:Array = Gaming.defineGroup.things.fatherArrObj["parts"];
         var numArr0:Array = str0.split(",");
         var num0:int = int(numArr0[0]);
         for each(d0 in arr0)
         {
            if(d0.isPartsSpecialB())
            {
               if(d0.name.indexOf("_") > 0)
               {
                  if(d0.itemsLevel == 1)
                  {
                     partsBag0 = Gaming.PG.da.partsBag;
                     partsBag0.addDataByName(d0.name,num0);
                  }
               }
            }
         }
         return "添加特殊零件：" + num0 + "个";
      }
      
      public function clearBag(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(Gaming.uiGroup.skillUI.visible)
         {
            Gaming.uiGroup.skillUI.wearBox.bagBox.fatherData.clearData();
            return "清除了技能背包";
         }
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].clearData();
            return "清除了" + label0 + "背包";
         }
         return null;
      }
      
      public function clearHouse(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.houseUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "House"].clearData();
            return "清除了" + label0 + "仓库";
         }
         return "";
      }
      
      public function setBagLock(str0:String, v0:int) : String
      {
         var label0:String = Gaming.uiGroup.bagUI.labelBox.nowLabel;
         if(label0 != "")
         {
            Gaming.PG.da[label0 + "Bag"].saveGroup.lockLen = v0;
            return "设置当前背包解锁位置数：" + v0;
         }
         return "";
      }
      
      public function sortByTime(str0:String, v0:int) : String
      {
         Gaming.uiGroup.bagUI.sortByTime();
         return "按照日期排序";
      }
      
      public function delNoPositionThings(str0:String, v0:int) : String
      {
         Gaming.PG.DATA.arms.delNoPositionItems();
         return "清除位置溢出的物品";
      }
      
      public function getThingsStrByPrice(str0:String, v0:int) : String
      {
         var s0:String = ThingsSemltCreator.getThingsStrByPrice(v0);
         SaveTestBox.addText(s0);
         return "生成价值为" + v0 + "的物品列表。";
      }
   }
}

